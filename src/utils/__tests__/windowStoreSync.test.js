/**
 * windowStoreSync 工具函数测试
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { 
  isInNewWindow, 
  getBookIdFromUrl, 
  safeJumpToSection 
} from '../windowStoreSync'

// Mock dependencies
vi.mock('@/api/book/reader.js', () => ({
  getChaptersSimple: vi.fn()
}))

vi.mock('@/utils/reader', () => ({
  generatePaidPageContent: vi.fn(() => ({ content: [] })),
  hasAccessToReadChapter: vi.fn(() => true)
}))

vi.mock('element-plus', () => ({
  ElMessage: {
    info: vi.fn(),
    success: vi.fn(),
    error: vi.fn()
  }
}))

describe('windowStoreSync', () => {
  let mockStore

  beforeEach(() => {
    mockStore = {
      comprehensiveChapterAndCatalogData: {
        chaptersData: []
      },
      comprehensiveBookData: {
        bookId: null,
        bookName: null,
        totalPages: 0
      },
      setChaptersData: vi.fn(),
      jumpToChapter: vi.fn(),
      jumpToPageBasedOnNodeId: vi.fn()
    }
  })

  describe('isInNewWindow', () => {
    it('should return true when chaptersData is empty', () => {
      expect(isInNewWindow(mockStore)).toBe(true)
    })

    it('should return false when chaptersData has data', () => {
      mockStore.comprehensiveChapterAndCatalogData.chaptersData = [{ chapterId: '1' }]
      expect(isInNewWindow(mockStore)).toBe(false)
    })
  })

  describe('getBookIdFromUrl', () => {
    beforeEach(() => {
      // Mock window.location.search
      delete window.location
      window.location = { search: '' }
    })

    it('should return bookId from k parameter', () => {
      window.location.search = '?k=123&other=value'
      expect(getBookIdFromUrl()).toBe('123')
    })

    it('should return bookId from bookId parameter', () => {
      window.location.search = '?bookId=456&other=value'
      expect(getBookIdFromUrl()).toBe('456')
    })

    it('should return null when no bookId found', () => {
      window.location.search = '?other=value'
      expect(getBookIdFromUrl()).toBe(null)
    })
  })

  describe('safeJumpToSection', () => {
    it('should throw error when store is null', async () => {
      await expect(safeJumpToSection(null, 'bookId', 'chapterId'))
        .rejects.toThrow('Store 实例不能为空')
    })

    it('should throw error when bookId is empty', async () => {
      await expect(safeJumpToSection(mockStore, '', 'chapterId'))
        .rejects.toThrow('书籍ID不能为空')
    })

    it('should throw error when chapterId is empty', async () => {
      await expect(safeJumpToSection(mockStore, 'bookId', ''))
        .rejects.toThrow('章节ID不能为空')
    })

    it('should jump to chapter when chapter exists and no domId', async () => {
      // Setup store with chapter data
      mockStore.comprehensiveChapterAndCatalogData.chaptersData = [
        { chapterId: 'chapter1' }
      ]
      mockStore.jumpToChapter.mockResolvedValue()

      await safeJumpToSection(mockStore, 'bookId', 'chapter1')

      expect(mockStore.jumpToChapter).toHaveBeenCalledWith('chapter1')
    })

    it('should jump to page when chapter exists and domId provided', async () => {
      // Setup store with chapter data
      mockStore.comprehensiveChapterAndCatalogData.chaptersData = [
        { chapterId: 'chapter1' }
      ]
      mockStore.jumpToPageBasedOnNodeId.mockResolvedValue()

      await safeJumpToSection(mockStore, 'bookId', 'chapter1', 'dom1')

      expect(mockStore.jumpToPageBasedOnNodeId).toHaveBeenCalledWith('dom1', 'chapter1')
    })

    it('should throw error when chapter does not exist', async () => {
      // Store has no chapters
      await expect(safeJumpToSection(mockStore, 'bookId', 'nonexistent'))
        .rejects.toThrow('目标章节不存在或无法访问')
    })
  })
})
