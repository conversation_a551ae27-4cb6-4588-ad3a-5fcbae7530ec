/**
 * 新窗口中 Store 数据同步工具
 * 用于解决 window.open 新窗口中 Vuex store 数据为空的问题
 */

import { getChaptersSimple } from '@/api/book/reader.js'
import { generatePaidPageContent, hasAccessToReadChapter } from '@/utils/reader'
import { ElMessage } from 'element-plus'
import { validateJumpRequest, quickDiagnose } from './jumpTestValidator'

/**
 * 检查当前是否在新窗口中（通过检查 store 数据是否为空）
 * @param {Object} store - Vuex store 实例
 * @returns {boolean} 是否在新窗口中
 */
export function isInNewWindow(store) {
  return !store.comprehensiveChapterAndCatalogData.chaptersData.length
}

/**
 * 在新窗口中初始化必要的 store 数据
 * @param {Object} store - Vuex store 实例
 * @param {string} bookId - 书籍ID
 * @returns {Promise} 初始化结果
 */
export async function initializeStoreDataInNewWindow(store, bookId) {
  try {
    // 如果数据已经存在，直接返回
    if (!isInNewWindow(store)) {
      return Promise.resolve()
    }

    ElMessage.info('正在初始化数据，请稍候...')

    // 获取章节数据
    const chaptersResponse = await getChaptersSimple(bookId)
    if (chaptersResponse.code === 200) {
      const chapterInfoArray = chaptersResponse.data || []
      let pagesCnt = 0
      
      // 处理章节数据，与 Content.vue 中的逻辑保持一致
      chapterInfoArray.forEach((chapterInfo) => {
        chapterInfo.catalogId = chapterInfo.chapterId
        chapterInfo.title = chapterInfo.chapterName || ""
        let chapterTotalPages = chapterInfo.chapterTotalPages || "1"
        chapterTotalPages = Number.isNaN(Number(chapterTotalPages))
          ? 1
          : Number(chapterTotalPages)
        chapterInfo.hasAccessToChapter = hasAccessToReadChapter(chapterInfo)
        chapterInfo.totalPages = chapterInfo.hasAccessToChapter
          ? chapterTotalPages
          : 1
        chapterInfo.children = chapterInfo.hasAccessToChapter
          ? chapterInfo.catalogs || []
          : []

        if (!chapterInfo.hasAccessToChapter) {
          chapterInfo.chapterContent = generatePaidPageContent()
        }
        pagesCnt += chapterTotalPages
        delete chapterInfo.catalogs
      })
      
      // 设置章节数据到 store
      store.setChaptersData(chapterInfoArray, pagesCnt)

      // 设置基本的书籍信息
      store.comprehensiveBookData.bookId = bookId

      // 设置简化模式（新窗口中通常使用简化模式）
      store.setSimplifiedMode(true)

      // 如果有第一个章节，设置为当前章节ID（避免 loadChapterData 中 this.chapterId 为空）
      if (chapterInfoArray.length > 0) {
        store.setCurrentChapterId(chapterInfoArray[0].chapterId)
      }

      ElMessage.success('数据初始化完成')
      return Promise.resolve()
    } else {
      throw new Error(chaptersResponse.msg || '获取章节数据失败')
    }
  } catch (error) {
    const errorMsg = '初始化数据失败: ' + error.message
    ElMessage.error(errorMsg)
    console.error('Store 数据初始化错误:', error)
    return Promise.reject(error)
  }
}

/**
 * 安全的章节跳转方法，自动处理新窗口数据初始化
 * @param {Object} store - Vuex store 实例
 * @param {string} bookId - 书籍ID
 * @param {string} chapterId - 章节ID
 * @param {string} domId - 可选的DOM ID，用于精确定位
 * @returns {Promise} 跳转结果
 */
export async function safeJumpToSection(store, bookId, chapterId, domId = null) {
  // 参数验证
  if (!store) {
    throw new Error('Store 实例不能为空')
  }
  if (!bookId) {
    throw new Error('书籍ID不能为空')
  }
  if (!chapterId) {
    throw new Error('章节ID不能为空')
  }

  try {
    console.log('🔍 安全跳转开始，参数:', { bookId, chapterId, domId })
    console.log('📊 当前 Store 状态:')
    console.log('  - 是否在新窗口:', isInNewWindow(store))
    console.log('  - 章节数据长度:', store.comprehensiveChapterAndCatalogData?.chaptersData?.length)
    console.log('  - 当前章节ID:', store.chapterId)
    console.log('  - 简化模式:', store.simplifiedMode)

    // 如果在新窗口中，先初始化数据
    if (isInNewWindow(store)) {
      console.log('🔄 检测到新窗口，开始初始化数据...')
      await initializeStoreDataInNewWindow(store, bookId)
    }

    // 确保 store 有基本的状态设置
    if (typeof store.simplifiedMode === 'undefined') {
      console.log('🔧 设置简化模式...')
      store.setSimplifiedMode(true)
    }

    // 检查章节是否存在
    const chapterExists = store.comprehensiveChapterAndCatalogData.chaptersData.some(item => {
      return item.chapterId == chapterId
    })

    console.log('📋 目标章节存在:', chapterExists)

    if (!chapterExists) {
      // 如果章节不存在，尝试重新加载章节数据
      console.log('⚠️ 目标章节不存在，尝试重新加载章节数据...')
      if (isInNewWindow(store)) {
        await initializeStoreDataInNewWindow(store, bookId)
        // 重新检查
        const recheckExists = store.comprehensiveChapterAndCatalogData.chaptersData.some(item => {
          return item.chapterId == chapterId
        })
        if (!recheckExists) {
          throw new Error('目标章节不存在或无法访问')
        }
      } else {
        throw new Error('目标章节不存在或无法访问')
      }
    }

    // 在跳转前确保设置当前章节ID（避免 loadChapterData 中 this.chapterId 为空）
    if (store.chapterId !== chapterId) {
      console.log('🔧 设置当前章节ID:', chapterId)
      store.setCurrentChapterId(chapterId)
    }

    console.log('🚀 开始执行跳转...')

    // 执行跳转
    if (domId) {
      console.log('📍 跳转到指定位置:', domId)
      return await store.jumpToPageBasedOnNodeId(domId, chapterId)
    } else {
      console.log('📖 跳转到章节:', chapterId)
      return await store.jumpToChapter(chapterId)
    }
  } catch (error) {
    console.error('❌ 安全跳转失败:', error)
    console.error('错误堆栈:', error.stack)
    throw error
  }
}

/**
 * 从 URL 参数中获取书籍ID
 * @returns {string|null} 书籍ID
 */
export function getBookIdFromUrl() {
  const urlParams = new URLSearchParams(window.location.search)
  return urlParams.get('k') || urlParams.get('bookId')
}

/**
 * 从 URL 参数中获取章节ID
 * @returns {string|null} 章节ID
 */
export function getChapterIdFromUrl() {
  const urlParams = new URLSearchParams(window.location.search)
  return urlParams.get('cid') || urlParams.get('chapterId')
}

/**
 * 从 URL 参数中获取目录ID
 * @returns {string|null} 目录ID
 */
export function getCatalogIdFromUrl() {
  const urlParams = new URLSearchParams(window.location.search)
  return urlParams.get('cataid') || urlParams.get('catalogId')
}

/**
 * 检查并自动初始化新窗口的 store 数据
 * 这个函数可以在组件的 mounted 钩子中调用
 * @param {Object} store - Vuex store 实例
 * @returns {Promise} 初始化结果
 */
export async function autoInitializeIfNeeded(store) {
  const bookId = getBookIdFromUrl()
  if (bookId && isInNewWindow(store)) {
    await initializeStoreDataInNewWindow(store, bookId)

    // 尝试从URL获取更多信息并设置
    const chapterIdFromUrl = getChapterIdFromUrl()
    if (chapterIdFromUrl) {
      // 验证章节是否存在
      const chapterExists = store.comprehensiveChapterAndCatalogData.chaptersData.some(item => {
        return item.chapterId == chapterIdFromUrl
      })
      if (chapterExists) {
        store.setCurrentChapterId(chapterIdFromUrl)
      }
    }

    return Promise.resolve()
  }
  return Promise.resolve()
}

/**
 * 方案二：通过 postMessage 在窗口间传递数据
 * 父窗口调用此方法向子窗口发送 store 数据
 * @param {Window} childWindow - 子窗口引用
 * @param {Object} storeData - 要传递的 store 数据
 */
export function sendStoreDataToChildWindow(childWindow, storeData) {
  if (childWindow && !childWindow.closed) {
    const message = {
      type: 'STORE_DATA_SYNC',
      data: {
        chaptersData: storeData.comprehensiveChapterAndCatalogData.chaptersData,
        bookId: storeData.comprehensiveBookData.bookId,
        bookName: storeData.comprehensiveBookData.bookName,
        totalPages: storeData.comprehensiveBookData.totalPages,
        chapterId: storeData.chapterId,
        simplifiedMode: storeData.simplifiedMode
      }
    }
    childWindow.postMessage(message, window.location.origin)
  }
}

/**
 * 子窗口监听父窗口发送的 store 数据
 * @param {Object} store - Vuex store 实例
 */
export function listenForStoreDataFromParent(store) {
  const handleMessage = (event) => {
    // 验证消息来源
    if (event.origin !== window.location.origin) {
      return
    }

    if (event.data.type === 'STORE_DATA_SYNC') {
      const { chaptersData, bookId, bookName, totalPages, chapterId, simplifiedMode } = event.data.data

      // 设置 store 数据
      store.setChaptersData(chaptersData, totalPages)
      store.comprehensiveBookData.bookId = bookId
      store.comprehensiveBookData.bookName = bookName

      // 设置关键状态
      if (chapterId) {
        store.setCurrentChapterId(chapterId)
      }
      if (typeof simplifiedMode !== 'undefined') {
        store.setSimplifiedMode(simplifiedMode)
      }

      console.log('从父窗口接收到 store 数据')
      ElMessage.success('数据同步完成')
    }
  }

  window.addEventListener('message', handleMessage)

  // 返回清理函数
  return () => {
    window.removeEventListener('message', handleMessage)
  }
}

/**
 * 检查当前是否在独立的新窗口中（不是iframe或嵌入页面）
 * @returns {boolean} 是否在独立新窗口中
 */
export function isInStandaloneWindow() {
  try {
    // 检查是否有父窗口且不是同一个窗口
    return window.opener && window.opener !== window
  } catch (e) {
    // 跨域情况下可能会抛出异常
    return false
  }
}

/**
 * 跳转到父窗口的阅读器页面
 * @param {string} bookId - 书籍ID
 * @param {string} chapterId - 章节ID
 * @param {string} domId - 可选的DOM ID
 * @returns {boolean} 是否成功发送跳转请求
 */
export function jumpToParentWindow(bookId, chapterId, domId = null) {

  if (!isInStandaloneWindow()) {
    console.log('❌ 不在独立窗口中，无法跳转到父窗口')
    return false
  }

  try {
    const jumpData = {
      type: 'JUMP_TO_SECTION',
      data: {
        bookId,
        chapterId,
        domId,
        timestamp: Date.now()
      }
    }

    // 向父窗口发送跳转请求
    window.opener.postMessage(jumpData, window.location.origin)

    // 延迟关闭窗口，确保消息已发送
    setTimeout(() => {
      window.close()
    }, 500) // 增加延迟时间

    return true
  } catch (error) {
    return false
  }
}

/**
 * 处理跳转请求的核心函数
 * @param {Object} store - Vuex store 实例
 * @param {string} bookId - 书籍ID
 * @param {string} chapterId - 章节ID
 * @param {string} domId - 可选的DOM ID
 * @returns {Promise} 跳转结果
 */
async function handleJumpRequest(store, bookId, chapterId, domId = null) {
  console.log('🎯 处理跳转请求:', { bookId, chapterId, domId })

  // 1. 执行快速诊断
  const validation = quickDiagnose(store, bookId, chapterId, domId)

  if (!validation.valid) {
    throw new Error(`跳转验证失败: ${validation.errors.join('; ')}`)
  }

  // 2. 确保基本状态设置
  if (typeof store.simplifiedMode === 'undefined') {
    console.log('🔧 设置简化模式为 false (主窗口)')
    store.setSimplifiedMode(false) // 主窗口通常不使用简化模式
  }

  // 3. 确保有章节数据
  if (!store.comprehensiveChapterAndCatalogData?.chaptersData?.length) {
    console.log('⚠️ 章节数据为空，这在主窗口中不应该发生')
    throw new Error('主窗口中章节数据为空，请刷新页面')
  }

  // 4. 检查目标章节是否存在
  const targetChapter = store.comprehensiveChapterAndCatalogData.chaptersData.find(
    chapter => chapter.chapterId === chapterId
  )

  if (!targetChapter) {
    console.log('❌ 目标章节不存在:', chapterId)
    console.log('📋 可用章节:', store.comprehensiveChapterAndCatalogData.chaptersData.map(c => c.chapterId))
    throw new Error(`目标章节 ${chapterId} 不存在`)
  }

  console.log('✅ 找到目标章节:', targetChapter.chapterName || targetChapter.title)

  // 5. 设置当前章节ID
  if (store.chapterId !== chapterId) {
    console.log('🔧 设置当前章节ID:', chapterId)
    store.setCurrentChapterId(chapterId)
  }

  // 6. 执行跳转
  try {
    if (domId) {
      console.log('📍 跳转到指定位置:', domId)
      await store.jumpToPageBasedOnNodeId(domId, chapterId)
    } else {
      console.log('📖 跳转到章节首页:', chapterId)
      await store.jumpToChapter(chapterId)
    }

    console.log('🎉 跳转执行完成')

  } catch (error) {
    console.error('❌ 跳转执行失败:', error)

    // 尝试简单的章节跳转作为备用方案
    if (domId) {
      console.log('🔄 尝试简单章节跳转作为备用方案...')
      await store.jumpToChapter(chapterId)
    } else {
      throw error
    }
  }
}

/**
 * 在父窗口中监听子窗口的跳转请求
 * @param {Object} store - Vuex store 实例
 * @returns {Function} 清理函数
 */
export function listenForJumpRequests(store) {
  console.log('🎧 开始监听子窗口跳转请求')

  const handleMessage = async (event) => {
    console.log('📨 收到消息:', event.data)

    // 验证消息来源
    if (event.origin !== window.location.origin) {
      console.warn('❌ 消息来源不匹配:', event.origin, 'vs', window.location.origin)
      return
    }

    if (event.data && event.data.type === 'JUMP_TO_SECTION') {
      const headingItem = event.data.data

      console.log('🎯 收到子窗口跳转请求:', headingItem)

      try {
        // 直接执行跳转
        if (headingItem.domId) {
          console.log('📍 跳转到指定位置:', headingItem.domId)
          await store.jumpToPageBasedOnNodeId(headingItem.domId, headingItem.chapterId)
        } else {
          console.log('📖 跳转到章节:', headingItem.chapterId)
          await store.jumpToChapter(headingItem.chapterId)
        }

        console.log('🎉 跳转成功')
        ElMessage.success('已跳转到指定位置')

      } catch (error) {
        console.error('❌ 处理跳转请求失败:', error)

        // 如果跳转失败，尝试简单的章节跳转
        if (headingItem.chapterId) {
          console.log('🔄 尝试简单章节跳转...')
          try {
            await store.jumpToChapter(headingItem.chapterId)
            ElMessage.success('已跳转到章节')
          } catch (fallbackError) {
            console.error('❌ 简单跳转也失败:', fallbackError)
            ElMessage.error('跳转失败，请手动导航到目标章节')
          }
        } else {
          ElMessage.error('跳转失败: ' + error.message)
        }
      }
    }
  }

  window.addEventListener('message', handleMessage)

  // 返回清理函数
  return () => {
    console.log('🔇 停止监听子窗口跳转请求')
    window.removeEventListener('message', handleMessage)
  }
}

/**
 * 备用跳转方法：直接在父窗口中打开新URL
 * @param {string} bookId - 书籍ID
 * @param {string} chapterId - 章节ID
 * @param {string} domId - 可选的DOM ID
 * @returns {boolean} 是否成功
 */
export function fallbackJumpToParent(bookId, chapterId, domId = null) {
  if (!isInStandaloneWindow()) {
    return false
  }

  try {
    const url = `/reader?k=${bookId}&cid=${chapterId}${domId ? `&domId=${domId}` : ''}`
    console.log('🔄 使用备用方案跳转到:', url)

    // 直接在父窗口中打开新URL
    window.opener.location.href = url

    // 关闭当前窗口
    setTimeout(() => {
      window.close()
    }, 100)

    return true
  } catch (error) {
    console.error('❌ 备用跳转方案失败:', error)
    return false
  }
}

/**
 * 智能跳转方法：根据当前环境选择最佳跳转策略
 * @param {Object} store - Vuex store 实例
 * @param {string} bookId - 书籍ID
 * @param {string} chapterId - 章节ID
 * @param {string} domId - 可选的DOM ID
 * @returns {Promise} 跳转结果
 */
export async function smartJumpToSection(store, bookId, chapterId, domId = null) {
  // 如果在独立新窗口中，尝试跳转到父窗口
  if (isInStandaloneWindow()) {

    // 首先尝试 postMessage 方式
    const success = jumpToParentWindow(bookId, chapterId, domId)
    if (success) {
      return Promise.resolve()
    }

    // 如果 postMessage 失败，尝试备用方案
    const fallbackSuccess = fallbackJumpToParent(bookId, chapterId, domId)
    if (fallbackSuccess) {
      return Promise.resolve()
    }
  }

  // 在当前窗口中执行跳转
  return await safeJumpToSection(store, bookId, chapterId, domId)
}

/**
 * 测试跳转功能是否正常工作
 * @param {Object} store - Vuex store 实例
 * @param {string} bookId - 书籍ID
 * @param {string} chapterId - 章节ID
 * @param {string} domId - 可选的DOM ID
 * @returns {Promise<boolean>} 测试结果
 */
export async function testJumpFunction(store, bookId, chapterId, domId = null) {
  console.group('🧪 跳转功能测试')

  try {
    console.log('📋 测试参数:', { bookId, chapterId, domId })

    // 1. 检查 store 状态
    debugStoreState(store, 'Before Jump Test')

    // 2. 检查必要的方法是否存在
    console.log('🔍 检查 Store 方法:')
    console.log('  - jumpToChapter:', typeof store.jumpToChapter)
    console.log('  - jumpToPageBasedOnNodeId:', typeof store.jumpToPageBasedOnNodeId)
    console.log('  - setCurrentChapterId:', typeof store.setCurrentChapterId)
    console.log('  - setSimplifiedMode:', typeof store.setSimplifiedMode)

    // 3. 执行跳转测试
    console.log('🚀 开始跳转测试...')
    await safeJumpToSection(store, bookId, chapterId, domId)

    console.log('✅ 跳转测试成功')
    debugStoreState(store, 'After Jump Test')

    console.groupEnd()
    return true

  } catch (error) {
    console.error('❌ 跳转测试失败:', error)
    console.groupEnd()
    return false
  }
}

/**
 * 调试工具：打印当前 store 的关键状态
 * @param {Object} store - Vuex store 实例
 * @param {string} context - 调试上下文信息
 */
export function debugStoreState(store, context = '') {
  console.group(`🔍 Store State Debug ${context ? `- ${context}` : ''}`)
  console.log('📚 Book ID:', store.comprehensiveBookData?.bookId)
  console.log('📖 Chapter ID:', store.chapterId)
  console.log('🔧 Simplified Mode:', store.simplifiedMode)
  console.log('📋 Chapters Data Length:', store.comprehensiveChapterAndCatalogData?.chaptersData?.length || 0)
  console.log('📄 Total Pages:', store.comprehensiveBookData?.totalPages)
  console.log('🌐 Is In New Window:', isInNewWindow(store))
  console.log('🪟 Is In Standalone Window:', isInStandaloneWindow())
  console.log('🔗 URL Book ID:', getBookIdFromUrl())
  console.log('🔗 URL Chapter ID:', getChapterIdFromUrl())

  // 显示章节列表（前5个）
  if (store.comprehensiveChapterAndCatalogData?.chaptersData?.length > 0) {
    console.log('📚 可用章节 (前5个):')
    store.comprehensiveChapterAndCatalogData.chaptersData.slice(0, 5).forEach((chapter, index) => {
      console.log(`  ${index + 1}. ${chapter.chapterName || chapter.title} (ID: ${chapter.chapterId})`)
    })
  }

  console.groupEnd()
}
