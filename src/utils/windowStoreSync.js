/**
 * 新窗口中 Store 数据同步工具
 * 用于解决 window.open 新窗口中 Vuex store 数据为空的问题
 */

import { getChaptersSimple } from '@/api/book/reader.js'
import { generatePaidPageContent, hasAccessToReadChapter } from '@/utils/reader'
import { ElMessage } from 'element-plus'

/**
 * 检查当前是否在新窗口中（通过检查 store 数据是否为空）
 * @param {Object} store - Vuex store 实例
 * @returns {boolean} 是否在新窗口中
 */
export function isInNewWindow(store) {
  return !store.comprehensiveChapterAndCatalogData.chaptersData.length
}

/**
 * 在新窗口中初始化必要的 store 数据
 * @param {Object} store - Vuex store 实例
 * @param {string} bookId - 书籍ID
 * @returns {Promise} 初始化结果
 */
export async function initializeStoreDataInNewWindow(store, bookId) {
  try {
    // 如果数据已经存在，直接返回
    if (!isInNewWindow(store)) {
      return Promise.resolve()
    }

    ElMessage.info('正在初始化数据，请稍候...')

    // 获取章节数据
    const chaptersResponse = await getChaptersSimple(bookId)
    if (chaptersResponse.code === 200) {
      const chapterInfoArray = chaptersResponse.data || []
      let pagesCnt = 0
      
      // 处理章节数据，与 Content.vue 中的逻辑保持一致
      chapterInfoArray.forEach((chapterInfo) => {
        chapterInfo.catalogId = chapterInfo.chapterId
        chapterInfo.title = chapterInfo.chapterName || ""
        let chapterTotalPages = chapterInfo.chapterTotalPages || "1"
        chapterTotalPages = Number.isNaN(Number(chapterTotalPages))
          ? 1
          : Number(chapterTotalPages)
        chapterInfo.hasAccessToChapter = hasAccessToReadChapter(chapterInfo)
        chapterInfo.totalPages = chapterInfo.hasAccessToChapter
          ? chapterTotalPages
          : 1
        chapterInfo.children = chapterInfo.hasAccessToChapter
          ? chapterInfo.catalogs || []
          : []

        if (!chapterInfo.hasAccessToChapter) {
          chapterInfo.chapterContent = generatePaidPageContent()
        }
        pagesCnt += chapterTotalPages
        delete chapterInfo.catalogs
      })
      
      // 设置章节数据到 store
      store.setChaptersData(chapterInfoArray, pagesCnt)
      
      // 设置基本的书籍信息
      store.comprehensiveBookData.bookId = bookId
      
      ElMessage.success('数据初始化完成')
      return Promise.resolve()
    } else {
      throw new Error(chaptersResponse.msg || '获取章节数据失败')
    }
  } catch (error) {
    const errorMsg = '初始化数据失败: ' + error.message
    ElMessage.error(errorMsg)
    console.error('Store 数据初始化错误:', error)
    return Promise.reject(error)
  }
}

/**
 * 安全的章节跳转方法，自动处理新窗口数据初始化
 * @param {Object} store - Vuex store 实例
 * @param {string} bookId - 书籍ID
 * @param {string} chapterId - 章节ID
 * @param {string} domId - 可选的DOM ID，用于精确定位
 * @returns {Promise} 跳转结果
 */
export async function safeJumpToSection(store, bookId, chapterId, domId = null) {
  // 参数验证
  if (!store) {
    throw new Error('Store 实例不能为空')
  }
  if (!bookId) {
    throw new Error('书籍ID不能为空')
  }
  if (!chapterId) {
    throw new Error('章节ID不能为空')
  }

  try {
    // 如果在新窗口中，先初始化数据
    if (isInNewWindow(store)) {
      await initializeStoreDataInNewWindow(store, bookId)
    }

    // 检查章节是否存在
    const chapterExists = store.comprehensiveChapterAndCatalogData.chaptersData.some(item => {
      return item.chapterId == chapterId
    })

    if (!chapterExists) {
      throw new Error('目标章节不存在或无法访问')
    }

    // 执行跳转
    if (domId) {
      return await store.jumpToPageBasedOnNodeId(domId, chapterId)
    } else {
      return await store.jumpToChapter(chapterId)
    }
  } catch (error) {
    console.error('安全跳转失败:', error)
    throw error
  }
}

/**
 * 从 URL 参数中获取书籍ID
 * @returns {string|null} 书籍ID
 */
export function getBookIdFromUrl() {
  const urlParams = new URLSearchParams(window.location.search)
  return urlParams.get('k') || urlParams.get('bookId')
}

/**
 * 检查并自动初始化新窗口的 store 数据
 * 这个函数可以在组件的 mounted 钩子中调用
 * @param {Object} store - Vuex store 实例
 * @returns {Promise} 初始化结果
 */
export async function autoInitializeIfNeeded(store) {
  const bookId = getBookIdFromUrl()
  if (bookId && isInNewWindow(store)) {
    return await initializeStoreDataInNewWindow(store, bookId)
  }
  return Promise.resolve()
}

/**
 * 方案二：通过 postMessage 在窗口间传递数据
 * 父窗口调用此方法向子窗口发送 store 数据
 * @param {Window} childWindow - 子窗口引用
 * @param {Object} storeData - 要传递的 store 数据
 */
export function sendStoreDataToChildWindow(childWindow, storeData) {
  if (childWindow && !childWindow.closed) {
    const message = {
      type: 'STORE_DATA_SYNC',
      data: {
        chaptersData: storeData.comprehensiveChapterAndCatalogData.chaptersData,
        bookId: storeData.comprehensiveBookData.bookId,
        bookName: storeData.comprehensiveBookData.bookName,
        totalPages: storeData.comprehensiveBookData.totalPages
      }
    }
    childWindow.postMessage(message, window.location.origin)
  }
}

/**
 * 子窗口监听父窗口发送的 store 数据
 * @param {Object} store - Vuex store 实例
 */
export function listenForStoreDataFromParent(store) {
  const handleMessage = (event) => {
    // 验证消息来源
    if (event.origin !== window.location.origin) {
      return
    }

    if (event.data.type === 'STORE_DATA_SYNC') {
      const { chaptersData, bookId, bookName, totalPages } = event.data.data

      // 设置 store 数据
      store.setChaptersData(chaptersData, totalPages)
      store.comprehensiveBookData.bookId = bookId
      store.comprehensiveBookData.bookName = bookName

      console.log('从父窗口接收到 store 数据')
      ElMessage.success('数据同步完成')
    }
  }

  window.addEventListener('message', handleMessage)

  // 返回清理函数
  return () => {
    window.removeEventListener('message', handleMessage)
  }
}
