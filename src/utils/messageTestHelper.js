/**
 * 消息传递测试工具
 * 用于调试窗口间的 postMessage 通信
 */

/**
 * 测试父窗口是否能接收消息
 */
export function testParentWindowMessage() {
  if (!window.opener) {
    console.error('❌ 没有父窗口')
    return false
  }

  try {
    const testMessage = {
      type: 'TEST_MESSAGE',
      data: {
        message: 'Hello from child window',
        timestamp: Date.now()
      }
    }

    console.log('📤 发送测试消息到父窗口:', testMessage)
    window.opener.postMessage(testMessage, window.location.origin)
    return true
  } catch (error) {
    console.error('❌ 发送测试消息失败:', error)
    return false
  }
}

/**
 * 在父窗口中监听测试消息
 */
export function listenForTestMessages() {
  const handleTestMessage = (event) => {
    if (event.origin !== window.location.origin) {
      return
    }

    if (event.data && event.data.type === 'TEST_MESSAGE') {
      console.log('✅ 父窗口收到测试消息:', event.data)
      alert('父窗口收到子窗口的测试消息: ' + event.data.data.message)
    }
  }

  window.addEventListener('message', handleTestMessage)

  return () => {
    window.removeEventListener('message', handleTestMessage)
  }
}

/**
 * 检查窗口关系
 */
export function checkWindowRelationship() {
  const info = {
    hasOpener: !!window.opener,
    isTop: window === window.top,
    origin: window.location.origin,
    href: window.location.href
  }

  console.log('🔍 窗口关系检查:', info)
  return info
}

/**
 * 手动触发跳转请求（用于测试）
 */
export function manualTriggerJumpRequest(bookId, chapterId, domId = null) {
  if (!window.opener) {
    console.error('❌ 没有父窗口，无法发送跳转请求')
    return false
  }

  const jumpData = {
    type: 'JUMP_TO_SECTION',
    data: {
      bookId,
      chapterId,
      domId,
      timestamp: Date.now(),
      manual: true // 标记为手动触发
    }
  }

  console.log('🎯 手动发送跳转请求:', jumpData)

  try {
    window.opener.postMessage(jumpData, window.location.origin)
    console.log('✅ 跳转请求已发送')
    return true
  } catch (error) {
    console.error('❌ 发送跳转请求失败:', error)
    return false
  }
}

/**
 * 创建调试面板
 */
export function createDebugPanel() {
  // 检查是否已存在调试面板
  if (document.getElementById('message-debug-panel')) {
    return
  }

  const panel = document.createElement('div')
  panel.id = 'message-debug-panel'
  panel.style.cssText = `
    position: fixed;
    top: 10px;
    right: 10px;
    width: 300px;
    background: white;
    border: 2px solid #ccc;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 10000;
    font-family: Arial, sans-serif;
    font-size: 12px;
  `

  const windowInfo = checkWindowRelationship()

  panel.innerHTML = `
    <h4 style="margin: 0 0 10px 0; color: #333;">消息调试面板</h4>
    <div style="margin-bottom: 10px;">
      <strong>窗口信息:</strong><br>
      有父窗口: ${windowInfo.hasOpener ? '是' : '否'}<br>
      是顶级窗口: ${windowInfo.isTop ? '是' : '否'}<br>
      当前域: ${windowInfo.origin}
    </div>
    <div style="margin-bottom: 10px;">
      <button onclick="window.messageTestHelper.testParentWindowMessage()" 
              style="margin-right: 5px; padding: 5px 10px; font-size: 11px;">
        测试消息
      </button>
      <button onclick="window.messageTestHelper.manualTriggerJumpRequest('test-book', 'test-chapter', 'test-dom')" 
              style="padding: 5px 10px; font-size: 11px;">
        测试跳转
      </button>
    </div>
    <button onclick="document.getElementById('message-debug-panel').remove()" 
            style="padding: 5px 10px; font-size: 11px; background: #ff4d4f; color: white; border: none; border-radius: 4px;">
      关闭面板
    </button>
  `

  document.body.appendChild(panel)

  // 将测试函数暴露到全局
  window.messageTestHelper = {
    testParentWindowMessage,
    manualTriggerJumpRequest,
    checkWindowRelationship
  }
}

/**
 * 自动创建调试面板（仅在开发环境）
 */
export function autoCreateDebugPanel() {
  // 检查是否在开发环境或有调试标志
  const isDev = process.env.NODE_ENV === 'development' || 
                window.location.search.includes('debug=true') ||
                localStorage.getItem('enableMessageDebug') === 'true'

  if (isDev) {
    // 延迟创建，确保页面已加载
    setTimeout(createDebugPanel, 1000)
  }
}
