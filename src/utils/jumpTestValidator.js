/**
 * 跳转功能验证工具
 * 用于验证跳转功能是否正常工作
 */

import { ElMessage } from 'element-plus'

/**
 * 验证 store 状态是否正确
 * @param {Object} store - Vuex store 实例
 * @returns {Object} 验证结果
 */
export function validateStoreState(store) {
  const results = {
    valid: true,
    errors: [],
    warnings: []
  }

  // 检查基本属性
  if (!store) {
    results.valid = false
    results.errors.push('Store 实例不存在')
    return results
  }

  // 检查书籍数据
  if (!store.comprehensiveBookData) {
    results.valid = false
    results.errors.push('comprehensiveBookData 不存在')
  } else {
    if (!store.comprehensiveBookData.bookId) {
      results.warnings.push('书籍ID未设置')
    }
  }

  // 检查章节数据
  if (!store.comprehensiveChapterAndCatalogData) {
    results.valid = false
    results.errors.push('comprehensiveChapterAndCatalogData 不存在')
  } else {
    if (!store.comprehensiveChapterAndCatalogData.chaptersData) {
      results.valid = false
      results.errors.push('chaptersData 不存在')
    } else if (store.comprehensiveChapterAndCatalogData.chaptersData.length === 0) {
      results.warnings.push('章节数据为空')
    }
  }

  // 检查必要的方法
  const requiredMethods = [
    'jumpToChapter',
    'jumpToPageBasedOnNodeId',
    'setCurrentChapterId',
    'setSimplifiedMode'
  ]

  requiredMethods.forEach(method => {
    if (typeof store[method] !== 'function') {
      results.valid = false
      results.errors.push(`方法 ${method} 不存在或不是函数`)
    }
  })

  return results
}

/**
 * 验证跳转参数是否有效
 * @param {string} bookId - 书籍ID
 * @param {string} chapterId - 章节ID
 * @param {string} domId - 可选的DOM ID
 * @returns {Object} 验证结果
 */
export function validateJumpParameters(bookId, chapterId, domId = null) {
  const results = {
    valid: true,
    errors: []
  }

  if (!bookId || typeof bookId !== 'string') {
    results.valid = false
    results.errors.push('书籍ID无效')
  }

  if (!chapterId || typeof chapterId !== 'string') {
    results.valid = false
    results.errors.push('章节ID无效')
  }

  if (domId && typeof domId !== 'string') {
    results.valid = false
    results.errors.push('DOM ID类型无效')
  }

  return results
}

/**
 * 验证章节是否存在
 * @param {Object} store - Vuex store 实例
 * @param {string} chapterId - 章节ID
 * @returns {Object} 验证结果
 */
export function validateChapterExists(store, chapterId) {
  const results = {
    valid: false,
    chapter: null,
    availableChapters: []
  }

  if (!store.comprehensiveChapterAndCatalogData?.chaptersData) {
    return results
  }

  const chapters = store.comprehensiveChapterAndCatalogData.chaptersData
  results.availableChapters = chapters.map(c => ({
    id: c.chapterId,
    name: c.chapterName || c.title
  }))

  const targetChapter = chapters.find(chapter => chapter.chapterId === chapterId)
  if (targetChapter) {
    results.valid = true
    results.chapter = targetChapter
  }

  return results
}

/**
 * 执行完整的跳转验证
 * @param {Object} store - Vuex store 实例
 * @param {string} bookId - 书籍ID
 * @param {string} chapterId - 章节ID
 * @param {string} domId - 可选的DOM ID
 * @returns {Object} 验证结果
 */
export function validateJumpRequest(store, bookId, chapterId, domId = null) {
  console.group('🔍 跳转请求验证')

  const results = {
    valid: true,
    errors: [],
    warnings: [],
    details: {}
  }

  // 1. 验证 store 状态
  console.log('1️⃣ 验证 Store 状态...')
  const storeValidation = validateStoreState(store)
  results.details.store = storeValidation
  if (!storeValidation.valid) {
    results.valid = false
    results.errors.push(...storeValidation.errors)
  }
  results.warnings.push(...storeValidation.warnings)

  // 2. 验证参数
  console.log('2️⃣ 验证跳转参数...')
  const paramValidation = validateJumpParameters(bookId, chapterId, domId)
  results.details.parameters = paramValidation
  if (!paramValidation.valid) {
    results.valid = false
    results.errors.push(...paramValidation.errors)
  }

  // 3. 验证章节存在性
  if (results.valid) {
    console.log('3️⃣ 验证章节存在性...')
    const chapterValidation = validateChapterExists(store, chapterId)
    results.details.chapter = chapterValidation
    if (!chapterValidation.valid) {
      results.valid = false
      results.errors.push(`章节 ${chapterId} 不存在`)
      console.log('📋 可用章节:', chapterValidation.availableChapters)
    }
  }

  // 输出验证结果
  if (results.valid) {
    console.log('✅ 验证通过，可以执行跳转')
  } else {
    console.log('❌ 验证失败:')
    results.errors.forEach(error => console.log(`  - ${error}`))
  }

  if (results.warnings.length > 0) {
    console.log('⚠️ 警告:')
    results.warnings.forEach(warning => console.log(`  - ${warning}`))
  }

  console.groupEnd()
  return results
}

/**
 * 显示验证结果给用户
 * @param {Object} validationResult - 验证结果
 */
export function showValidationResult(validationResult) {
  if (validationResult.valid) {
    ElMessage.success('跳转验证通过')
  } else {
    const errorMsg = validationResult.errors.join('; ')
    ElMessage.error(`跳转验证失败: ${errorMsg}`)
  }

  if (validationResult.warnings.length > 0) {
    const warningMsg = validationResult.warnings.join('; ')
    ElMessage.warning(`注意: ${warningMsg}`)
  }
}

/**
 * 快速诊断跳转问题
 * @param {Object} store - Vuex store 实例
 * @param {string} bookId - 书籍ID
 * @param {string} chapterId - 章节ID
 * @param {string} domId - 可选的DOM ID
 */
export function quickDiagnose(store, bookId, chapterId, domId = null) {
  console.group('🩺 快速诊断')

  console.log('📋 诊断信息:')
  console.log('  - 当前URL:', window.location.href)
  console.log('  - 有父窗口:', !!window.opener)
  console.log('  - Store 实例:', !!store)
  console.log('  - 书籍ID:', bookId)
  console.log('  - 章节ID:', chapterId)
  console.log('  - DOM ID:', domId)

  if (store) {
    console.log('  - Store 书籍ID:', store.comprehensiveBookData?.bookId)
    console.log('  - Store 当前章节:', store.chapterId)
    console.log('  - 章节数据长度:', store.comprehensiveChapterAndCatalogData?.chaptersData?.length)
  }

  const validation = validateJumpRequest(store, bookId, chapterId, domId)
  
  console.log('🎯 诊断结论:')
  if (validation.valid) {
    console.log('✅ 跳转应该可以正常工作')
  } else {
    console.log('❌ 跳转可能失败，原因:')
    validation.errors.forEach(error => console.log(`  - ${error}`))
    
    console.log('💡 建议解决方案:')
    if (validation.details.store && !validation.details.store.valid) {
      console.log('  - 检查 Store 初始化')
      console.log('  - 确保在正确的页面中')
    }
    if (validation.details.chapter && !validation.details.chapter.valid) {
      console.log('  - 检查章节ID是否正确')
      console.log('  - 确保章节数据已加载')
    }
  }

  console.groupEnd()
  return validation
}
