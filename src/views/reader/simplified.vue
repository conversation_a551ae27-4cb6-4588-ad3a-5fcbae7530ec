<template>
  <div
    class="simplified-reader-wrapper __reader__"
    :reader-theme="store.theme"
    rer="reader"
  >
    <Header
      v-if="operationFlag"
      :messageTag="false"
      :show-message-hint="false"
      :simple-preview-mode="true"
      :show-reading-progress="false"
      :show-ai-assistent="false"
      :show-book-lines="false"
      :show-book-marks="false"
      :show-return-to-home="false"
      :showTourGuide="false"
      :show-search="false"
    />
    <div class="reader-main">
      <div class="content">
        <Menus
          v-if="operationFlag"
          ref="menusRef"
          :showRelatedBooks="false"
          :show-knowledge-graph="false"
        />
        <!-- __hidden-scroll__     隐藏滚动条样式 -->
        <main
          class="content-pages"
          ref="pageContentContainer"
          :style="store.styleSetting"
          :id="PAGE_ITEMS_CONTAINER_ID"
        >
          <reflowable-layout
            v-if="store.pageFlippingMethod === 'y'"
            :has-book-mark="false"
          />
          <folio-simulation
            v-else-if="store.pageFlippingMethod === 'r'"
            :has-book-mark="false"
          />
        </main>
      </div>
      <imagePreview></imagePreview>
      <PreviewDialog />
    </div>

  </div>
</template>

<script setup>
import Header from "./sub/Header.vue";
import useReader, {READING_MODE} from '@/store/modules/reader';
import {onMounted, ref, watch, onBeforeUnmount, getCurrentInstance, provide, nextTick} from 'vue';
import Menus from './sub/Menus.vue'
import imagePreview from '@/components/galleryImagePreview/index.vue'
import ReflowableLayout from './sub/Pages/ReflowableLayout.vue'
import FolioSimulation from './sub/Pages/FolioSimulation.vue'
import PreviewDialog from './sub/Tool/PreviewDialog.vue'
import {
  PAGE_ITEMS_CONTAINER_ID,
  generatePaidPageContent,
  hasAccessToReadChapter, PAGE_ITEMS_CSS_CLASS, BOOK_PAGE_INDEX, PAGE_TURNING_ERROR_MAPPER, highlightKeyWordSmooth,
} from "@/utils/reader";
import { getChaptersSimple } from "@/api/book/reader";
import { ElMessage, ElMessageBox } from "element-plus";
import {throttle} from "lodash";
import useAutoReading from "@/store/modules/autoReading.js";
import printJS from "print-js";
// import print from 'print-js'

provide("simplifiedReadingMode", true);
const { proxy } = getCurrentInstance();
const store = useReader();
const props = defineProps({
  initChapterId: {
    type: String,
    default: "",
  },
  initCataId: {
    type: String,
    default: "",
  },
});
const menusRef = ref(null);
const operationFlag = ref(true);
const pageContentContainer = ref("pageContentContainer");
watch(
  () => store.styleSetting["line-height"],
  (newVal, oldVal) => {
    var element = document.getElementById(PAGE_ITEMS_CONTAINER_ID);
    var pTags = element.querySelectorAll("p");
    // console.log(pTags)
    pTags.forEach(function (pTag) {
      pTag.style.lineHeight = store.styleSetting["line-height"];
    });
  },
  { deep: true }
);

window.addEventListener('message', function(event) {
  if (event.data && event.data.type === 'SCROLL_TO_ELEMENT') {
    const domId = event.data.payload.domId;
    let targetHeadingDom = document.querySelector(
        `:is(h1, h2, h3, h4, h5, h6)[data-toc-id="${domId}"]`
    );

    // 节头跳转问题修改
    if (!targetHeadingDom) {
      targetHeadingDom = document.querySelector(
          `div[id="${domId}"]`
      );
    }
    if (targetHeadingDom) {
      highlightKeyWordSmooth(targetHeadingDom);
    }
  }
}, false);

window.addEventListener('message', function (event) {
  // 可选：校验 origin 安全性
  if (event.data.type === 'PRINT') {
    // printJS(PAGE_ITEMS_CONTAINER_ID,'html');
    const container = document.getElementById(PAGE_ITEMS_CONTAINER_ID);
    const canvases = container.querySelectorAll('canvas');
    const canvasArray = Array.from(canvases);

    // 移除 canvas
    canvasArray.forEach(canvas => canvas.remove());
    printJS({
      printable: PAGE_ITEMS_CONTAINER_ID, // 元素 ID
      type: 'html',
      scanStyles: false, // 避免样式丢失
      ignoreElements: ['canvas'], // 忽略所有 canvas 元素
      targetStyles: ['*'],
      onPrintDialogClose: () => {
        // 打印完成后恢复 canvas
        canvasArray.forEach(canvas => container.appendChild(canvas));
      }
    });

  }
});


onMounted(() => {
  const bookId = proxy.$route.query.k;
  const chapterId = proxy.$route.query.cid;
  const fromType = proxy.$route.query.fromType;
  operationFlag.value = proxy.$route.query.operationFlag === 'false' ? false : true;

  if (!bookId || !chapterId) {
    ElMessage.error("教材id和章节id不可以为空");
  }
  store.setCommonData({
    bookId,
  });
  getChapterList(bookId, chapterId, fromType).then(() => {
    store.setSimplifiedMode(true);
    store.jumpToChapter(chapterId, 1);
  });
  document.title = "章节预览";
  nextTick(() => {
    pageContentContainer.value.addEventListener("scroll", scrollCallback);
    pageContentContainer.value.addEventListener("wheel", wheelCallback);
  });
});


const mousedown = (e) => {
  start.x = e.clientX;
  start.y = e.clientY;
};
function calculatePreviousPageHeight(allPageNodes, currentIndexInTheNodeArray) {
  let heightInTotal = 0;
  for (let i = currentIndexInTheNodeArray; i > -1; i--) {
    heightInTotal += allPageNodes[i].offsetHeight;
  }
  return heightInTotal;
}
function hit2Top(scrollTopValue) {
  if (scrollTopValue === 0) {
    return true;
  }
  return false;
}
function hit2Bottom(
    pageIndexInContainer,
    pageTotalInView,
    containerHeight,
    remainingVisibleHeightOfCurrentPageInContainer
) {
  // console.log('判断是否到底页面底部：', pageIndexInContainer === (pageTotalInView - 1), Math.abs(remainingVisibleHeightOfCurrentPageInContainer - containerHeight))
  if (
      pageIndexInContainer === pageTotalInView - 1 &&
      Math.abs(remainingVisibleHeightOfCurrentPageInContainer - containerHeight) <
      80
  ) {
    return true;
  }
  return false;
}

const autoReadingStore = useAutoReading();
let loadNewChapterTimer = null;
let cumulatedHitTimesOnWill = 0;
let need2Jump2NextChapter = false;
let need2Jump2PreviousChapter = false;
let loadingChapterInProgress = false;
let pageScrollDirection = "";
function autoReadingLoadChapter(nextChapterOrPreviousChapter) {
  clearTimeout(loadNewChapterTimer);
  if (store.reading === "automaticReading") {
    loadNewChapterTimer = setTimeout(() => {
      if (nextChapterOrPreviousChapter === "next") {
        if (!store.isLastChapter()) {
          ElMessage.info("即将进入下一章");
        }
        store.nextPage().catch((error) => {
          if (error.code === PAGE_TURNING_ERROR_MAPPER.LAST_PAGE_IN_BOOK.code) {
            autoReadingStore.pause();
            ElMessage.info("您已阅读到教材最后一页，5秒后将退出自动阅读");
            setTimeout(() => {
              store.setReading(READING_MODE.GENERAL);
            }, 5000);
          }
          return error;
        });
      } else if (nextChapterOrPreviousChapter === "previous") {
        // console.log('去往上一页')
        store.lastPage();
      }
    }, 2000);
  }
}
const scrollCallback = throttle(
    () => {
      // console.log('scrollCallback')
      // 真实阅读模式，不需要计算当前滑动到第几页, 也不会触发这里scroll事件
      const pageContainer = document.querySelector(`#${PAGE_ITEMS_CONTAINER_ID}`);
      const containerHeight = pageContainer.offsetHeight;
      const pagesDom = document.querySelectorAll(
          `#${PAGE_ITEMS_CONTAINER_ID} .${PAGE_ITEMS_CSS_CLASS}`
      );
      let scrollTopValue = pageContainer.scrollTop;
      let targetPage = null;

      // 自动阅读模式只能向下浏览，不会向上浏览
      if (hit2Top(scrollTopValue) && store.reading !== "automaticReading") {
        need2Jump2PreviousChapter = true;
        need2Jump2NextChapter = !need2Jump2PreviousChapter;
        autoReadingLoadChapter("previous");
        // console.log('hit2top { need2Jump2PreviousChapter }:', need2Jump2PreviousChapter)
      }
      // 当页面滑动到最底部或最顶部的时候，该事件不会被继续触发, 所以仅在scroll事件触发的时候会将cumulatedHitTimesOnWill次数设置为0
      cumulatedHitTimesOnWill = 0;
      for (
          let i = 0,
              len = pagesDom.length,
              remainedVisibleHeightOfCurrentPageInContainer;
          i < len;
          i++
      ) {
        // 当前页内容仍然占据整个容器高度
        // console.log('data value:', scrollTopValue, pagesDom[i].offsetHeight, containerHeight)
        const cumulatedPreviousPageHeight = calculatePreviousPageHeight(
            pagesDom,
            i - 1
        );
        const remainingScrollValue =
            scrollTopValue - cumulatedPreviousPageHeight - pagesDom[i].offsetHeight;
        if (remainingScrollValue > 0) {
          // 当前node完全消失在容器内, 则继续计算
          // scrollTopValue = remainingScrollValue
          continue;
        }
        remainedVisibleHeightOfCurrentPageInContainer =
            cumulatedPreviousPageHeight + pagesDom[i].offsetHeight - scrollTopValue;
        if (remainedVisibleHeightOfCurrentPageInContainer >= containerHeight) {
          // 当前页面占据整个容器
          targetPage = pagesDom[i].getAttribute(BOOK_PAGE_INDEX);
          // break
        } else if (
            remainedVisibleHeightOfCurrentPageInContainer < containerHeight &&
            remainedVisibleHeightOfCurrentPageInContainer > containerHeight / 2
        ) {
          // 当前页面占据整个容器的二分之一以上
          targetPage = pagesDom[i].getAttribute(BOOK_PAGE_INDEX);
          // break
        } else if (
            remainedVisibleHeightOfCurrentPageInContainer <
            containerHeight / 2
        ) {
          // 当前页面占据整个容器不到二分之一
          if (i < len) {
            targetPage = pagesDom[i + 1].getAttribute(BOOK_PAGE_INDEX);
            // break
          } else {
            // 按理说不应该进入这个分支, 需要检查页面显示效果是否正确
            // debugger
          }
          break;
        } else {
          // 按理说不应该进入这个分支,当前页面不可能占据页面不到二分之一，又大于整个容器
          // debugger
          // if (i === (len - 1) && remainedVisibleHeightOfCurrentPageInContainer <= 100) {
          //   targetPage = pagesDom[i].getAttribute(BOOK_PAGE_INDEX)
          //   // 加载下一章
          // }
        }
        // pageDom的长度和store.pageData的长度一致
        if (
            hit2Bottom(
                i,
                len,
                containerHeight,
                remainedVisibleHeightOfCurrentPageInContainer
            )
        ) {
          need2Jump2NextChapter = true;
          need2Jump2PreviousChapter = !need2Jump2NextChapter;
          autoReadingLoadChapter("next");
          // console.log('hit2bottom { need2Jump2NextChapter}:', need2Jump2NextChapter)
        }
        if (targetPage != null) {
          break;
        }
      }
      if (targetPage != null) {
        // 阅读器在自动阅读状态下切换翻页方式，由于使用了节流throttle，所以这个scrollCallback会有延迟, 会导致targetPage是null，最优化的方式是修改当前页码的判断
        store.setCurrentPageIndex(targetPage);
      }
    },
    400,
    {
      trailing: true,
    }
);

const wheelCallback = throttle(
    (event) => {
      if (loadingChapterInProgress) {
        promptUserWithChapterLoading();
        return;
      }
      // 自动阅读模式下，鼠标滚轮事件不会被触发
      // console.log('wheelcallback主动触发')
      if (event.deltaY < 0) {
        pageScrollDirection = "up";
      } else {
        pageScrollDirection = "down";
      }
      if (need2Jump2PreviousChapter && pageScrollDirection === "up") {
        cumulatedHitTimesOnWill++;
        // console.log('下滑主动加载前一章节意愿次数:', cumulatedHitTimesOnWill)
        if (cumulatedHitTimesOnWill > 2) {
          cumulatedHitTimesOnWill = 0;
          loadingChapterInProgress = true;
          return store
              .lastPage()
              .catch((error) => {
                if (
                    error.type === PAGE_TURNING_ERROR_MAPPER.FIRST_PAGE_IN_BOOK.code
                ) {
                  ElMessage.warning(
                      PAGE_TURNING_ERROR_MAPPER.FIRST_PAGE_IN_BOOK.msg
                  );
                }
                return error;
              })
              .finally(() => {
                loadingChapterInProgress = false;
              });
        }
      } else if (need2Jump2NextChapter && pageScrollDirection === "down") {
        cumulatedHitTimesOnWill++;
        // console.log('上滑主动加载前一章节意愿次数:', cumulatedHitTimesOnWill)
        if (cumulatedHitTimesOnWill > 2) {
          cumulatedHitTimesOnWill = 0;
          loadingChapterInProgress = true;
          return store.nextPage().finally(() => {
            loadingChapterInProgress = false;
          });
        }
      }
    },
    200,
    {
      trailing: true,
    }
);

watch(
    () => store.pageFlippingMethod,
    (nValue) => {
      if (nValue === "r") {
        // 真实分页后不通过鼠标滚轮事件更新当前第几页
        pageContentContainer.value.removeEventListener("scroll", scrollCallback);
        pageContentContainer.value.removeEventListener("wheel", wheelCallback);
      } else {
        nextTick(() => {
          pageContentContainer.value.addEventListener("scroll", scrollCallback);
          pageContentContainer.value.addEventListener("wheel", wheelCallback);
        });
      }
    }
);

onBeforeUnmount(() => {
  ElMessageBox.close();
  store.deauthorizeToTheBook();
});

// 加载目录信息
function getChapterList(bookId, chapterId, fromType) {
  let chapterInfoArray = [];
  if (!fromType) {
    fromType = 1;
  }
  return getChaptersSimple(bookId, fromType).then((res) => {
    if (res.code === 200) {
      chapterInfoArray = res.data || [];
      chapterInfoArray = chapterInfoArray.filter(
        (chapterItemData) => chapterItemData.chapterId === chapterId
      );
      let pagesCnt = 0;
      chapterInfoArray.forEach((chapterInfo) => {
        chapterInfo.catalogId = chapterInfo.chapterId;
        chapterInfo.title = chapterInfo.chapterName || "";
        let chapterTotalPages = chapterInfo.chapterTotalPages || "1";
        chapterTotalPages = Number.isNaN(Number(chapterTotalPages))
          ? 1
          : Number(chapterTotalPages);
        chapterInfo.hasAccessToChapter = hasAccessToReadChapter(chapterInfo);
        chapterInfo.totalPages = chapterInfo.hasAccessToChapter
          ? chapterTotalPages
          : 1;
        chapterInfo.children = chapterInfo.hasAccessToChapter
          ? chapterInfo.catalogs || []
          : [];

        if (!chapterInfo.hasAccessToChapter) {
          chapterInfo.chapterContent = generatePaidPageContent();
        }
        pagesCnt += chapterTotalPages;
        delete chapterInfo.catalogs;
      });
      store.setChaptersData(chapterInfoArray, pagesCnt);
    } else {
      proxy.$modal.msgError(res.msg);
    }
  });
}
</script>
<style lang="scss" scoped>
@import "./sub/readerTheme.scss";
.__reader__ {
  width: 100vw;
  height: 100vh;
  background-color: var(--pageBackgroundColor);
  .reader-main {
    overflow: hidden;
    width: 100%;
    height: calc(100% - 48px);
  }
}
.content {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;
  .content-pages {
    width: 100%;
    height: 100%;
    min-height: 800px;
    overflow-y: auto;
  }
}
.simplified-reader-wrapper {
  .Bookmark {
    display: none;
  }
}
// 自定义滚动条样式
.content-pages {
  &::-webkit-scrollbar {
    width: 10px !important;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 5px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c0c0c0;
    border-radius: 5px;

    &:hover {
      background: #a0a0a0;
    }
  }
}
</style>
