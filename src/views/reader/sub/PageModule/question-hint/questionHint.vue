<style lang="less">
.question-hint {
  .sectionReferTo {
    list-style: none;
    margin: 0;
    padding: 0;
    li {
      .el-breadcrumb__item .el-breadcrumb__inner {
        cursor: pointer;
        &:hover {
          color: #428bd7;
        }
      }
      &.jumping {
        .el-breadcrumb__item .el-breadcrumb__inner {
          cursor: not-allowed;
          opacity: 0.6;
        }
      }
      .el-breadcrumb {
        line-height: 1.5;
      }
      .window-tip {
        font-size: 12px;
        color: #909399;
        margin-top: 5px;
        padding-left: 10px;
        i {
          margin-right: 4px;
        }
      }
      padding: 10px 0 0 10px;
    }
  }
}
</style>
<template>
  <el-tabs
    v-model="activeName"
    class="question-hint"
    v-if="analysis || sectionReferToData?.length > 0">
    <el-tab-pane label="查看解析"
      name="ana"
      v-if="analysis">
      <div
        class="analysisItem"
        v-html="analysis">
      </div>
    </el-tab-pane>
    <el-tab-pane label="查看知识点"
      name="referTo"
      v-if="sectionReferToData?.length > 0">
      <ul
        class="sectionReferTo">
        <li
          v-for="(sectionItem) in sectionReferToData">
          <el-breadcrumb
            separator="/"
            @click="clickToGoToReferredSection(sectionItem)">
            <el-breadcrumb-item
              v-for="(chapterItem) in sectionItem">
              {{ chapterItem.name }}
            </el-breadcrumb-item>
          </el-breadcrumb>
          <!-- 新窗口提示 -->
          <div v-if="isInStandaloneWindow()" class="window-tip">
            <i class="el-icon-info"></i>
            点击将跳转到主阅读器页面
          </div>
        </li>
      </ul>
    </el-tab-pane>
  </el-tabs>
</template>
<script setup>
import { defineProps, ref, onMounted } from 'vue'
import useReader from '@/store/modules/reader'
import { HEADING_ATTR_ID, highlightKeyWordSmooth } from '@/utils/reader';
import { PAGE_TURNING_ERROR_MAPPER } from '@/utils/reader'
import { ElMessage } from 'element-plus'
import { isInStandaloneWindow } from '@/utils/windowStoreSync'

const store = useReader()
const props = defineProps({
  sectionReferToData: {
    type: Object,
    default: () => {
      return []
    }
  },
  analysis: {
    type: String,
    default: ''
  }
})
const activeName = ref('ana')

// 组件挂载
onMounted(() => {
  console.log('questionHint 组件已挂载')
})

async function clickToGoToReferredSection(clickedSectionItem) {
  const headingItem = clickedSectionItem[clickedSectionItem.length - 1]

  try {
    // 如果在独立新窗口中，直接发送消息给父窗口
    if (isInStandaloneWindow()) {
      console.log('🚀 发送跳转请求到父窗口:', headingItem)

      // 发送消息给父窗口
      window.opener.postMessage({
        type: 'JUMP_TO_SECTION',
        data: headingItem
      }, window.location.origin)

      ElMessage.success('正在跳转到主阅读器页面...')

      // 延迟关闭窗口
      setTimeout(() => {
        window.close()
      }, 500)

    } else {
      // 在当前窗口中跳转
      if (headingItem.domId) {
        await store.jumpToPageBasedOnNodeId(headingItem.domId, headingItem.chapterId)
        // 高亮显示
        const level = clickedSectionItem.length - 1
        const targetHeadingDom = document.querySelector(`h${level}[${HEADING_ATTR_ID}="${headingItem.domId}"]`)
        if (targetHeadingDom) {
          highlightKeyWordSmooth(targetHeadingDom)
        }
      } else {
        await store.jumpToChapter(headingItem.chapterId)
      }
    }

  } catch (err) {
    if (err.code === PAGE_TURNING_ERROR_MAPPER.CHAPTER_DATA_NOT_EXIST.code) {
      ElMessage.error(PAGE_TURNING_ERROR_MAPPER.CHAPTER_DATA_NOT_EXIST.msg)
    } else {
      ElMessage.error(err.message || '跳转失败，请稍后重试')
    }
    console.error('跳转错误:', err)
  }
}
</script>