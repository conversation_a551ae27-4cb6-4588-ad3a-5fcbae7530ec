<style lang="less">
.question-hint {
  .sectionReferTo {
    list-style: none;
    margin: 0;
    padding: 0;
    li {
      .el-breadcrumb__item .el-breadcrumb__inner {
        cursor: pointer;
        &:hover {
          color: #428bd7;
        }
      }
      &.jumping {
        .el-breadcrumb__item .el-breadcrumb__inner {
          cursor: not-allowed;
          opacity: 0.6;
        }
      }
      .el-breadcrumb {
        line-height: 1.5;
      }
      .window-tip {
        font-size: 12px;
        color: #909399;
        margin-top: 5px;
        padding-left: 10px;
        i {
          margin-right: 4px;
        }
      }
      padding: 10px 0 0 10px;
    }
  }
}
</style>
<template>
  <el-tabs
    v-model="activeName"
    class="question-hint"
    v-if="analysis || sectionReferToData?.length > 0">
    <el-tab-pane label="查看解析"
      name="ana"
      v-if="analysis">
      <div
        class="analysisItem"
        v-html="analysis">
      </div>
    </el-tab-pane>
    <el-tab-pane label="查看知识点"
      name="referTo"
      v-if="sectionReferToData?.length > 0">
      <ul
        class="sectionReferTo">
        <li
          v-for="(sectionItem) in sectionReferToData">
          <el-breadcrumb
            separator="/"
            @click="clickToGoToReferredSection(sectionItem)"
            :class="{ 'jumping': isJumping }">
            <el-breadcrumb-item
              v-for="(chapterItem) in sectionItem">
              {{ chapterItem.name }}
              <span v-if="isJumping" style="margin-left: 5px;">
                <i class="el-icon-loading"></i>
              </span>
            </el-breadcrumb-item>
          </el-breadcrumb>
          <!-- 新窗口提示 -->
          <div v-if="isInStandaloneWindow()" class="window-tip">
            <i class="el-icon-info"></i>
            点击将跳转到主阅读器页面
          </div>
        </li>
      </ul>
    </el-tab-pane>
  </el-tabs>
</template>
<script setup>
import { defineProps, ref, onMounted } from 'vue'
import useReader from '@/store/modules/reader'
import { HEADING_ATTR_ID, highlightKeyWordSmooth } from '@/utils/reader';
import { PAGE_TURNING_ERROR_MAPPER } from '@/utils/reader'
import { ElMessage } from 'element-plus'
import { smartJumpToSection, getBookIdFromUrl, getChapterIdFromUrl, autoInitializeIfNeeded, debugStoreState, isInStandaloneWindow } from '@/utils/windowStoreSync'
import { autoCreateDebugPanel } from '@/utils/messageTestHelper'

const store = useReader()
const props = defineProps({
  sectionReferToData: {
    type: Object,
    default: () => {
      return []
    }
  },
  analysis: {
    type: String,
    default: ''
  }
})
const activeName = ref('ana')
const isJumping = ref(false) // 跳转加载状态

// 组件挂载时自动检查并初始化数据
onMounted(async () => {
  debugStoreState(store, 'Component Mounted - Before Init')

  try {
    await autoInitializeIfNeeded(store)
    debugStoreState(store, 'Component Mounted - After Init')
  } catch (error) {
    console.warn('自动初始化失败，将在点击时重试:', error)
    debugStoreState(store, 'Component Mounted - Init Failed')
    // 不显示错误消息，因为这只是预加载，用户点击时会重试
  }
})

async function clickToGoToReferredSection(clickedSectionItem) {
  const headingItem = clickedSectionItem[clickedSectionItem.length - 1]

  isJumping.value = true

  try {
    debugStoreState(store, 'Before Jump')

    // 获取书籍ID，优先使用 headingItem 中的 bookId，否则从 URL 获取
    const bookId = headingItem.bookId || getBookIdFromUrl()

    if (!bookId) {
      ElMessage.error('无法获取书籍信息')
      return
    }

    console.log('🎯 准备跳转到:', {
      bookId,
      chapterId: headingItem.chapterId,
      domId: headingItem.domId,
      isInStandaloneWindow: isInStandaloneWindow()
    })

    // 使用智能跳转方法，自动选择最佳跳转策略
    await smartJumpToSection(store, bookId, headingItem.chapterId, headingItem.domId)

    debugStoreState(store, 'After Jump')

    // 如果有 domId，进行高亮显示
    if (headingItem.domId) {
      // 参考知识点保存了目录的完整路径，有层级概念，所以具体是h几看位置就可以
      const level = clickedSectionItem.length - 1
      const targetHeadingDom = document.querySelector(`h${level}[${HEADING_ATTR_ID}="${headingItem.domId}"]`)
      if (targetHeadingDom) {
        highlightKeyWordSmooth(targetHeadingDom)
      }
    }
  } catch (err) {
    if (err.code === PAGE_TURNING_ERROR_MAPPER.CHAPTER_DATA_NOT_EXIST.code) {
      ElMessage.error(PAGE_TURNING_ERROR_MAPPER.CHAPTER_DATA_NOT_EXIST.msg)
    } else {
      ElMessage.error(err.message || '跳转失败，请稍后重试')
    }
    console.error('跳转错误:', err)
  } finally {
    isJumping.value = false
  }
}
</script>