<style lang="scss" scoped>
.question-content {
  max-width: 100%;
  position: relative;
  padding-bottom: 50px;
  z-index: 0;
}
.footer {
  text-align: center;
}
.data-item {
  position: relative;
  margin-left: 7%;
  margin-right: 6% !important;
  min-height: 100px;
  padding: 60px 25px 25px 45px;
  margin-top: -60px;
  background: #ffffff;
  border-radius: 12px;
  border: 2px solid;
  color:black;
  z-index: 2;
}

.data-item-jump {
  position: relative;
  margin-left: 7%;
  margin-right: 6% !important;
  min-height: 100px;
  padding: 60px 25px 25px 45px;
  margin-top: 10px;
  background: #ffffff;
  border-radius: 12px;
  border: 2px solid;
  color:black;
  z-index: 2;
}

.data-item-em {
  font-weight: bold;
  font-size: 22px;
  font-style: normal;
  text-transform: none;
  line-height: 1.5;
  margin-bottom: 20px;
  :deep(img) {
    max-width: 100%;
  }
  :deep(code) {
    white-space: pre-wrap;
    word-wrap: break-word;
    word-break: break-all;
  }
}

.data-item-hid {
  position: absolute; /* 绝对定位 */
  min-height: 120px;
  top: 60px; /* 向下偏移 5px */
  left: 105px; /* 向右偏移 5px */
  right: 65px; /* 使其宽度与父元素相同 */
  bottom: 40px; /* 使其高度与父元素相同 */
  background-color: #ADBCFF; /* 背景色为紫色 */
  border-radius: 12px; /* 与 .data-item 相同的圆角 */
  z-index: 1; /* 使其位于 .data-item 的后面 */
  border: 2px solid black;
}

.radio-back {
  background: linear-gradient( 90deg, #57BCFF 0%, #B7CEFF 100%);
  border-radius: 8px 8px 8px 8px;
  border: 2px solid #C9F9FF;
  padding:20px;
  width: 100%;
  color: #ffffff;
}

.radio-back:hover {
  background: #4FB3FF !important;
  border-color: #C9F9FF;
}

.custom-btn {
  width: 200px;
  height: 50px;
  background: linear-gradient(to bottom, #F1F6FF, #4F9CF4, #3676ED, #C2E1FF); /* 渐变背景 */
  border: none; /* 去掉默认边框 */
  color: white; /* 字体颜色 */
  padding: 10px 20px; /* 内边距 */
  font-size: 20px; /* 字体大小 */
  border-radius: 30px; /* 圆角 */
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.2); /* 阴影效果 */
  transition: background 0.3s, transform 0.2s; /* 背景和变换过渡效果 */
}

/* 悬停效果 */
.custom-btn:hover {
  background: linear-gradient(to bottom, #A0BFFF, #0056b3); /* 悬停时的渐变背景 */
  transform: translateY(-2px); /* 悬停时轻微上移 */
}

/* 点击效果 */
.custom-btn:active {
  transform: translateY(2px); /* 点击时下移 */
}

.shadow-on-hover {
  box-shadow: 0 4px 6px rgba(0,0,0,.1), 0 2px 4px rgba(0,0,0,.5);
  transition: all .3s ease;
}

.shadow-on-hover:hover {
  background-color: #2ED2A9; /* 可以根据需要调整颜色 */
  color: white;
  box-shadow: 0 8px 12px rgba(0,0,0,.2), 0 4px 8px rgba(0,0,0,.12); /* 更明显的阴影 */
}

.bg-color-1 {
  background-color: #A283FF;
  border-radius: 8px;
  border: 2px solid black;
  padding: 20px;
  width: 100%;
}

.bg-color-2 {
  background-color: #67B8FF;
  border-radius: 8px;
  border: 2px solid black;
  padding: 20px;
  width: 100%;
}

.bg-color-1:hover {
  background-color: #C0ABFF;
  border-radius: 8px;
  border: 2px solid black;
  padding: 20px;
  width: 100%;
  transform: translateY(-2px);
}

.bg-color-1:checked {
  background-color: #C0ABFF;
  border-radius: 8px;
  border: 2px solid black;
  padding: 20px;
  width: 100%;
}

.bg-color-2:hover {
  background-color: #C0ABFF;
  border-radius: 8px;
  border: 2px solid black;
  padding: 20px;
  width: 100%;
  transform: translateY(-2px);
}

.bg-color-red{
  background-color: #C0ABFF;
  border-radius: 8px;
  border: 2px solid black;
  padding: 20px;
  width: 100%;
}

.el-button {
  border-color: #2ED2A9;
}

.button-container {
  display: flex; /* 使用flexbox布局 */
  justify-content: flex-end; /* 靠右对齐 */
  margin-right: 50px;
  gap: 40px; /* 按钮间距为20px */
}

:deep(.el-radio) {
  margin-right: 20px !important; /* 保持你需要的间距 */
  padding-left: 20px;

  &:last-child {
    margin-right: 20px !important; /* 强制覆盖默认的 0 */
  }
}

:deep(.el-radio__input) {
  display: none;
}

.custom-radio {
  position: relative; /* 确保容器中内容的定位正确 */
}

:deep(.el-radio__inner) {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 20px; /* 圆圈的大小 */
  height: 20px; /* 圆圈的大小 */
  border-radius: 50%;
  border: 2px solid #85DBFD; /* 圆圈的边框颜色 */
  background-color: red; /* 初始状态下圆圈是透明的 */
  transition: all 0.3s ease;
}
.radio-icon{
  width: 20px;
  height: 20px;
  margin-right: 8px;
  margin-left: -10px;
}

.radio-content {
  display: flex; /* 水平布局 */
  align-items: center; /* 垂直居中对齐 */
}
</style>
<template>
  <section class="questionType">
    <div class="question-content">
      <div :class="{'data-item': scaleType === 1, 'data-item-jump': scaleType === 2}">
        <div class="data-item-em">
          <div v-html="currentData.questionContent"></div>
        </div>
        <div class="data-item-em">
          <!-- 单选框 -->
          <el-radio-group v-model="selectedOptionId" @change="cacheAnswer" style="gap: 10px;width: 100%">
            <el-radio v-for="(option, index) in currentData.moocPsychologyHealthScaleQuestionOption"
                      :key="option.optionId" :label="option.optionId" :class="getBackgroundColor(index)" class="custom-radio">
              <div class="radio-content">
                <img
                    v-if="selectedOptionId === option.optionId"
                    src="@/assets/images/readerResourcesIcon/psy-checked.png"
                    alt="图标"
                    class="radio-icon"
                />
                <div v-else class="radio-icon"></div>
                <div style="color: black">
                  {{ getOptionLabel(index) }}. {{ option.optionContent }}
                </div>
              </div>
            </el-radio>
          </el-radio-group>
        </div>
      </div>
      <div class="data-item-hid"></div>
    </div>
    <!-- 控制按钮：上一题与下一题 -->
    <div class="footer" style="margin-top: 30px;margin-right: 50px">
      <div class="button-container">
        <!--  顺序作答  -->
        <el-button :disabled="currentIndex === 0" v-if="!jump" class="custom-btn shadow-on-hover" round @click="prevQuestion">上一题</el-button>
        <el-button :disabled="currentIndex === count - 1" v-if="!jump" class="custom-btn shadow-on-hover" @click="nextQuestion">下一题</el-button>
        <!--  跳转作答  -->
        <el-button :disabled="currentIndex === 0" v-if="jump" class="custom-btn shadow-on-hover" round @click="prevJumpQuestion ">上一题</el-button>
        <el-button :disabled="jump && isEnd" v-if="jump" class="custom-btn shadow-on-hover" @click="umpToQuestion">下一题</el-button>
        <el-button v-if="jump && isEnd" class="custom-btn shadow-on-hover" @click="commitAll">提交</el-button>
        <t-button v-if="!jump && currentIndex == count - 1" class="custom-btn shadow-on-hover" @click="commitAll">提交</t-button>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref, computed, defineProps, defineEmits, onMounted } from "vue";
import {ElMessage} from "element-plus";

const emit = defineEmits(["submitTool"]);
const props = defineProps({
  data: Array,
  footer: {
    type: Boolean,
    default: true,
  },
  questionSort: {
    type: Number,
    default: null,
  },
  count: {
    type: Number,
    default: null,
  },
  scaleId: {
    type: String,
    default: null,
  },
  ableToResubmit: {
    type: Boolean,
    default: false,
  },
  scaleType: {
    type: Number,
    default: false,
  }
});

// 当前显示的索引
const currentIndex = ref(0);
const currentData = computed(() => props.data[currentIndex.value]);
const selectedOptionId = ref(null);
const allAnswerList = ref([]);
const jump = ref(false);
const isEnd = ref(false);
const jumpToIndex = ref(1);
const jumpToQuestion = ref(null);
const previousIndexes = ref([]); // 用于保存跳转作答的上一题索引
const hoverIndex = ref(null);// 用于跟踪鼠标悬停的选项

const getBackgroundColor = (index) => {
  // const colors = ['#C0ABFF', '#67B8FF', '#A283FF'];
  const colors = ['bg-color-1', 'bg-color-2'];
  const baseColor = colors[index % colors.length];
  // 如果当前选中或鼠标悬停，返回红色背景
  if (selectedOptionId.value === currentData.value.moocPsychologyHealthScaleQuestionOption[index].optionId || hoverIndex.value === index) {
    return 'bg-color-red';
  }
  return baseColor; // 默认背景色
};

// 缓存选项
const cacheAnswer = () => {
  if (jump.value) {
    // 获取要跳转的题号
    jumpToQuestion.value = currentData.value.moocPsychologyHealthScaleQuestionOption.find(option => option.optionId === selectedOptionId.value).jumpQuestionId
    jumpToIndex.value = props.data.findIndex(item => item.questionId === jumpToQuestion.value);
    if (jumpToQuestion.value==-1) {
      isEnd.value = true
    } else {
      isEnd.value = false
    }
  }
  if (selectedOptionId.value) {
    // 获取选中的答案
    const selectedAnswer = currentData.value.moocPsychologyHealthScaleQuestionOption.find(
        option => option.optionId === selectedOptionId.value
    );

    if (selectedAnswer) {
      // 查找已选的答案项，确保更新
      const existingAnswer = allAnswerList.value.find(item => item.questionId === currentData.value.questionId);

      const score = selectedAnswer.isCorrect ? 1 : 0;  // 假设 `isCorrect` 是选项的一个属性，表示答案是否正确

      if (existingAnswer) {
        // 如果已有答案，更新选项 ID 和分数
        existingAnswer.optionId = selectedOptionId.value;
        existingAnswer.score = selectedAnswer.score;
      } else {
        // 如果没有答案，则添加新的答案项
        allAnswerList.value.push({
          questionId: currentData.value.questionId,
          optionId: selectedOptionId.value,
          score: selectedAnswer.score, // 新添加的分数
        });
      }
    }
  }
};

// 生成字母标签
const getOptionLabel = (index) => {
  let label = '';
  let i = index;
  while (i >= 0) {
    label = String.fromCharCode((i % 26) + 65) + label;
    i = Math.floor(i / 26) - 1;
  }
  return label;
};

// 上一题
const prevQuestion = () => {
  if (currentIndex.value > 0) {
    currentIndex.value--;
    updateSelectedOption();
    emit("submitTool", currentIndex.value);
    if (props.questionSort === 2) {
      emit("getFacet", currentData.value.facetName);
    }
  }
};

// 跳转作答上一题
const prevJumpQuestion = () => {
  if (previousIndexes.value.length > 0) {
    currentIndex.value = previousIndexes.value.pop();
    updateSelectedOption();
    emit("submitTool", currentIndex.value);
    if (props.questionSort === 2) {
      emit("getFacet", currentData.value.facetName);
    }
  }
}

//是否为跳转作答
const isJump = () => {
  if(props.scaleType === 2){
    jump.value = true
  } else {
    jump.value = false
  }
}

// 下一题
const nextQuestion = () => {
  if (currentIndex.value < props.count - 1) {
    currentIndex.value++;
    updateSelectedOption();
    emit("submitTool", currentIndex.value);
    if (props.questionSort === 2) {
      emit("getFacet", currentData.value.facetName);
    }
  }
};

// 跳转作答下一题
const umpToQuestion = () => {
  if (!selectedOptionId.value) {
    ElMessage.error('请选择选项！');
    return;
  }
  // 保存当前题号用于返回
  previousIndexes.value.push(currentIndex.value);
  // 获取跳转题号
  const selectedOption = currentData.value.moocPsychologyHealthScaleQuestionOption.find(
      option => option.optionId === selectedOptionId.value
  );
  if (selectedOption.jumpQuestionId === -1) {
    isEnd.value = true;
    return;
  }
  // 查找跳转题目索引
  const jumpIndex = props.data.findIndex(
      item => item.questionId === selectedOption.jumpQuestionId
  );
  if (jumpIndex !== -1) {
    currentIndex.value = jumpIndex;
    updateSelectedOption();
    emit("submitTool", currentIndex.value);
    if (props.questionSort === 2) {
      emit("getFacet", currentData.value.facetName);
    }
  }
};

// 根据当前题目索引更新选中的数据
const updateSelectedOption = () => {
  const selected = allAnswerList.value.find(item => item.questionId === currentData.value.questionId);
  if (selected) {
    selectedOptionId.value = selected.optionId; // 反显上次选中的选项
    if (jump) {
      let jump = selected.jumpQuestionId;
      if (jump.value==-1) {
        isEnd.value = true
      } else {
        isEnd.value = false
      }
    }
  } else {
    selectedOptionId.value = null; // 清除选项（如果没有选中任何选项）
  }
};

const commitAll = () => {
  emit("commitAll", allAnswerList.value);
  // 计算得分
  // 计算总分
  const totalScore = allAnswerList.value.reduce((total, answer) => total + answer.score, 0);

};

// 每次组件加载时尝试反显上次选中的选项
onMounted(() => {
  isJump()
  updateSelectedOption();
});

</script>
