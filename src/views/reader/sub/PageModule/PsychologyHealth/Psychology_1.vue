<template>
  <div
      class="Papers __orderTemplateBgUrl__"
      >
    <div
        class="collapse-main">
      <div class="icon">
        <img
            :src="templateStyle.theme == 'light' ? lightIcon : darkIcon"/>
      </div>
      <div class="name">
        {{ psychologyHealth.scaleName }}
      </div>
    </div>
    <div class="__name__" link
         @click="previewResource">
      打开
    </div>
  </div>

  <!-- 单维度 -->
  <el-dialog v-model="isShow" :style="dialogStyle"
             :append-to-body="true"
             :close-on-click-modal="false"
             :destroy-on-close="true"
             @close="closeHandler">
    <main
        class="PapersContent"
        v-if="!showResult"
        :class="{'begin-btn': startFlag, 'answer-btn': !startFlag}"
        :key="keys">
      <div>
        <div v-if="startFlag" class="container">
          <div class="scName">
            <div style="justify-content: center !important;"><p class="start-scale">{{ scaleForm.scaleName }}</p></div>
          </div>
<!--          <div class="overlap-box"></div>11-->
          <div :class="{'overlap-box-big': scaleForm.scaleName.length > 12, 'overlap-box': scaleForm.scaleName.length <= 12}"></div>
          <div class="start-scan-question-container">
            <div class="start-scan-question">
              <span class="dot">●</span>
              <div v-html="`<div style='font-size: 22px;'>${scanQuestion}</div>`"></div>
            </div>
            <div class="start-scan-question">
              <span class="dot">●</span>
              <div v-html="`<div style='font-size: 22px;'>${evaluationMethod}</div>`"></div>
            </div>
          </div>
          <div class="start-btn">
            <t-button theme="primary" size="large" shape="round" variant="base" class="custom-button" @click="startTest">开始测评</t-button>
          </div>
        </div>
        <div v-if="!startFlag">
          <div>
            <div class="start-facet" v-if="scaleForm.questionSort === 2 && scaleForm.showSortType === 1">
              <p>
                {{ facetName }}
              </p>
            </div>
            <div style="text-align: right;margin-right: 20px" >
              <p class="start-question">{{ scaleForm.scaleName }}</p>
            </div>
          </div>
          <div class="progress-container" v-if="scaleForm.scaleType === 1">
            <div class="progress-text"><span style="text-align: center">{{ currentIndex }}</span></div>
            <div class="rectangle">
              <el-progress :percentage="progressPercent" show-text="true" :format="formatProgress" trackColor="#AEE8FF"
                          color="#85DBFD" :stroke-width="16"
                          class="custom-progress" theme="line" />
            </div>
          </div>
          <div class="content">
            <component
                :is="PsychologyTopic_1"
                :scaleId="scaleForm.scaleId"
                :questionSort="scaleForm.questionSort"
                :scaleType="scaleForm.scaleType"
                :data="dataList"
                :count="count"
                :footer="false"
                @submitTool="submitTool"
                @commitAll="commitAll"
                @getFacet="getFacet"
            />
          </div>
        </div>
      </div>
    </main>
    <main
        class="PapersContent"
        v-if="showResult"
        :class="{'result': showResult}"
        :key="keys">
      <div v-if="!showDescribe && scaleForm.questionSort === 1">
        <div style="position: absolute;top: 46%;left: 50%;transform: translate(-50%, -50%);"><span style="font-size: 120px;font-weight: bold;">{{ sumScore }}</span><span style="font-size: 30px">分</span></div>
        <div style="position: absolute;top: 85%;left: 45%;">
          <t-button :disabled="currentIndex === 0" class="custom-btn" round @click="prevQuestion">查看结果</t-button>
        </div>
      </div>
      <div style="text-align: center;margin-top: 50px" v-else-if="!showDescribe && scaleForm.questionSort === 2 && scaleForm.showSumScore === 1">
        <!--    多维度显示结果    -->
        <div class="container-result">
          <div class="column column-1">
            <!-- 第一列的内容 -->
            <div :class="{'module1': facetList.length <= 4, 'module2': facetList.length > 4}">
              <div :class="{'colu1': facetList.length <= 4, 'colu2': facetList.length > 4}">
                <span style="color: black;font-size: 30px;font-weight: bold">总得分</span>
                <div class="image-container">
                  <img style="width: 300px; height: 300px;" src="@/assets/images/readerResourcesIcon/psy-score.png" alt="result">
                  <div class="score"><span style="font-size: 100px">{{ sumScore?sumScore:0 }}</span><span style="font-size: 30px">分</span></div>
                </div>
              </div>
            </div>
          </div>

          <div class="column column-2" v-if="facetList.length <= 4">
            <!-- 第二列的四行内容 -->
            <div v-for="(option, index) in facetList" class="rows-container">
              <div class="row">
                <div class="left-item">{{ option.facetName }}</div>
                <div class="right-item">得分：{{option.sumScore?option.sumScore:0}}</div>
              </div>
            </div>
          </div>
          <div class="column-row column-3" v-if="facetList.length > 4">
            <!-- 第二列的多行内容 -->
            <div v-for="(option, index) in facetList" class="rows-container-cell">
              <div class="else-row">
                <div class="cell">
                  <div class="left-item-cell">{{ option.facetName }}</div>
                  <div class="right-item-cell">得分：{{option.sumScore?option.sumScore:0}}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="buttons" style="margin-top: 60px; text-align: center;">
          <t-button :disabled="currentIndex === 0" class="custom-btn shadow-on-hover" round @click="prevQuestion">查看结果</t-button>
        </div>
      </div>
      <div style="text-align: center;margin-top: 50px" v-else-if="!showDescribe && scaleForm.questionSort === 2 && scaleForm.showSumScore === 2">
        <!--    多维度不显示得分    -->
        <div class="column column-2" style="margin-left: 30%" v-if="facetList && facetList.length <= 4">
          <!-- 第二列的四行内容 -->
          <div v-for="(option, index) in facetList" class="rows-container">
            <div class="row-unshow">
              <div class="left-item">{{ option.facetName }}</div>
              <div class="right-item">得分：{{option.sumScore?option.sumScore:0}}</div>
            </div>
          </div>
          <div style="position: absolute;top: 75%;left: 42%;">
            <t-button :disabled="currentIndex === 0" class="custom-btn shadow-on-hover" round @click="prevQuestion">查看结果</t-button>
          </div>
        </div>
        <div class="column-3-unshow" style="width: 60%;margin-left: 19%;margin-top: 100px" v-if="facetList.length > 4">
          <!-- 第二列的多行内容 -->
          <div v-for="(option, index) in facetList" class="rows-container-cell-unshow">
            <div>
              <div class="cell-unshow">
                <div class="left-item-cell">{{ option.facetName }}</div>
                <div class="right-item-cell">得分：{{option.sumScore?option.sumScore:0}}</div>
              </div>
            </div>
          </div>
          <div style="position: absolute;top: 80%;left: 42%;">
            <t-button :disabled="currentIndex === 0" class="custom-btn shadow-on-hover" round @click="prevQuestion">查看结果</t-button>
          </div>
        </div>
      </div>
      <div v-if="showDescribe">
        <div class="image-container-result" style="position: relative; justify-content: center; align-items: center; display: flex; margin-top: 60px;margin-bottom: 60px">
          <!-- 第一张图片 -->
          <img style="width: 950px; z-index: 1;" src="@/assets/images/readerResourcesIcon/psychology-result.png" alt="result">

          <!-- 第二张图片，位置绝对定位在第一张图片之内 -->
          <img style="width: 278px; height: 62px; position: absolute; top: 35px; z-index: 2;" src="@/assets/images/readerResourcesIcon/psychology-result1.png" alt="result">

          <!-- 第三部分文字，出现在第二张图片内，位置绝对定位 -->
          <span class="score" style="position: absolute; top: 65px; z-index: 3; color: #333333; font-size: 34px;">测试结果</span>

          <!-- 第四部分文字，出现在第一张图片内 -->
<!--          <div class="end-scan-question" style="position: absolute; text-align: left; z-index: 3; top: 60px; left: 100px; display: flex; flex-direction: column; align-items: flex-start;">
            <div v-html="evaluateReference"></div>
          </div>-->
          <div class="end-scan-question" style="position: absolute; z-index: 3; top: 60px; left: 100px; flex-direction: column; align-items: flex-start;width: 865px">
            <div v-if="evaluateReference && evaluateReference.replace(/[\r\n]/g, '').length >40" v-html="evaluateReference" style="text-align: left"></div>
            <div v-else v-html="evaluateReference" style="text-align: center"></div>
          </div>
        </div>
        <t-button class="custom-btn shadow-on-hover" style="margin-top: 60px;margin-right: 30px" round @click="showScore">查看得分</t-button>
        <t-button class="custom-btn shadow-on-hover" style="margin-top: 60px;margin-left: 30px" round @click="restartTest">重新测评</t-button>
      </div>
    </main>
  </el-dialog>
</template>

<script setup>
import lightIcon from "@/assets/resources/light/Psychology.svg";
import darkIcon from "@/assets/resources/dark/Psychology.svg";
import useReader from "@/store/modules/reader.js";
import {getPhychology} from '@/api/book/reader'
import {defineProps, onMounted, ref, computed} from "vue";
import PsychologyTopic_1 from "@/views/reader/sub/PageModule/PsychologyHealth/PsychologyTopic_1.vue";
import {saveScore} from "@/api/book/psychology.js";
import {HEADING_ATTR_ID} from "@/utils/reader.js";
import {ElMessage} from "element-plus";

import backgroundImage1 from "@/assets/images/readerResourcesIcon/psychology-background.png"
import backgroundImage2 from "@/assets/images/readerResourcesIcon/psychology-background1.png"
import backgroundImage3 from "@/assets/images/readerResourcesIcon/psychology-background2.png"
import backgroundImage4 from "@/assets/images/readerResourcesIcon/psychology-background3.png"
import backgroundImage5 from "@/assets/images/readerResourcesIcon/psychology-background4.png"


const store = useReader();
const isShow = ref(false);
const startFlag = ref(true);
const keys = ref(1);
const ableToResubmit = ref(true);
const showResult = ref(false);
const showDescribe = ref(false);
const scaleForm = ref({});
const dataList = ref([]);
const currentIndex = ref(1)
const sumScore = ref(0)
const count = ref(0)
const facetName = ref('')
const props = defineProps({
  id: String,
  psychologyHealth: Object,
});
const facetList = ref([])
const templateStyle = computed(() => {
  const obj = {};
  let tmpStyle = store.templateStyle;
  obj.theme = tmpStyle?.theme;
  return obj;
});

const evaluationMethod = computed(() => {
  return scaleForm.value?.evaluationMethod ? scaleForm.value.evaluationMethod.replace(/\n/g, '<br/>') : '';
});

const scanQuestion = computed(() => {
  return scaleForm.value?.scanQuestion ? scaleForm.value.scanQuestion.replace(/\n/g, '<br/>') : '';
});

const evaluateReference = computed(() => {
  return scaleForm.value?.evaluateReference ? scaleForm.value.evaluateReference.replace(/\n/g, '<br/>') : '';
});

const dialogStyle = computed(() => {
  let backgroundColor = '';
  if (showResult.value && scaleForm.value.questionSort === 1 && !showDescribe.value) {
    // 单维度
    return {
      'backgroundImage': `url(${backgroundImage5})`,
      'backgroundSize': '100% 100%',
      'backgroundPosition': 'center',
      'width': '1382px',
      'height': '775px'
    }
  } else if (showResult.value && scaleForm.value.questionSort === 2 && scaleForm.value.showSumScore === 2 && !showDescribe.value) {
    // 多维度不显示得分
    return {
      'backgroundImage': `url(${backgroundImage4})`,
      'backgroundSize': '100% 100%',
      'backgroundPosition': 'center',
      'width': '1382px',
      'height': '775px'
    };
  } else if (showDescribe.value) {
    // 显示结果描述
    return {
      'backgroundImage': `url(${backgroundImage3})`,
      'backgroundSize': '100% 100%',
      'backgroundPosition': 'center',
      'width': '1382px',
      'height': '775px'
    };
  } else if (startFlag.value) {
    return {
      'backgroundImage': `url(${backgroundImage1})`,
      'backgroundSize': '100% 100%',
      'backgroundPosition': 'center',
      'width': '1382px',
      'height': '775px'
    };
  } else if (showResult.value) {
    if (scaleForm.value.questionSort == 1) {
      // 单维度

    } else if (scaleForm.value.questionSort == 2 && scaleForm.value.showSumScore == 1) {
      // 多维度显示得分
      return {
        'backgroundImage': `url(${backgroundImage2})`,
        'backgroundSize': '100% 100%',
        'backgroundPosition': 'center',
        'width': '1382px',
        'height': '775px'
      };
    } else {
      // 多维度不显示得分

    }
  } else {
    backgroundColor = '#85DBFD'; // 纯蓝色背景
    return {
      'backgroundColor': backgroundColor,
      'width': '1200px',
      'height': '800px',
      'overflow': 'auto'
    };
  }
});

const commitAll = async (obj) => {
  sumScore.value = obj.reduce((sum, item) => {
    return sum + item.score;
  }, 0);
  showResult.value = true;
  facetList.value.forEach((facet) => {
    let list = dataList.value.filter(item => item.facetId === facet.facetId);
    const questionIds = list.map(item => item.questionId);
    const totalScore = obj
        .filter(item => questionIds.includes(item.questionId))
        .reduce((sum, item) => sum + item.score, 0);
    facet.sumScore = totalScore
  })
  if (ableToResubmit.value) {
    /*记录分数*/
    const param = {
      scaleId:props.psychologyHealth.scaleId,
      score:sumScore.value,
      moocPsychologyHealthScaleQuestion:obj,
      chapterId:store.chapterId,
      domId:props.id
    }
    let response = await saveScore(param)
    if (response.code === 500) {
      ElMessage.error("保存成绩失败")
    }
  }
};

onMounted(() => {
  const simplified = inject("simplifiedReadingMode");
  if (simplified) {
    ableToResubmit.value = false;
  }
});

const formatProgress = (percentage) => {
  return `${currentIndex.value}/${count.value}`; // 返回分子分母的字符串*/
};


const restartTest = () => {
  startFlag.value = true;
  currentIndex.value = 1
  sumScore.value = 0
  showResult.value = false
  showDescribe.value = false
}

const submitTool = (obj) => {
  currentIndex.value = obj + 1

};

const prevQuestion = () => {
  showDescribe.value = true
}

const previewResource = async () => {
  await getPsychologyHealth();
  isShow.value = true;
  count.value = dataList.value.length;
};

const getFacet = (obj) => {
  facetName.value = obj
}

const startTest = () => {
  startFlag.value = false;
};

const showScore = () => {
  showResult.value = true;
  showDescribe.value = false
}

const getPsychologyHealth = async () => {
  const list = await getPhychology(props.psychologyHealth.scaleId);
  scaleForm.value = list.data.scale;
  if (scaleForm.value.questionSort === 1) {
    dataList.value = list.data.questionList;
  } else {
    dataList.value = list.data.questionList.flatMap(item =>
        item.moocPsychologyHealthScaleQuestion.map(facet => ({
          ...facet,
          facetName: item.facetName,
          facetId: item.facetId
        }))).sort((a, b) => a.questionSort - b.questionSort);
    facetName.value = list.data.questionList[0].facetName;
    facetList.value = list.data.questionList.map(item => ({
      facetName: item.facetName,
      facetId: item.facetId
    }))
  }
};

const closeHandler = () => {
  keys.value++
  resetData()
  restartTest()
}
const resetData = () => {
  currentIndex.value = 1
  sumScore.value = 0
  showResult.value = false
  startFlag.value = true;
}

const progressPercent = computed(() => {
  return Math.round((currentIndex.value / count.value) * 100);
});
</script>

<style scoped lang="scss">

:deep(.no-border-dialog) {
  border: none !important;
  box-shadow: none !important;
  background: transparent !important;
}

:deep(.no-border-dialog .el-dialog__header) {
  display: none;
}

:deep(.no-border-dialog .el-dialog__body) {
  padding: 0 !important;
  margin: 0 !important;
  height: 100%;
  overflow: hidden;
}

.no-border-dialog.el-dialog {
  border: none !important;
  box-shadow: none !important;
}

.result-round{
  height: 350px;
  width: 350px;
  margin-top: 50px;
  margin-left: 32%;
  justify-content: center;
  align-items: center;
  display: flex;
  background: #65BAFF;
  box-shadow: 0px 0px 45px 0px rgba(59,115,238,0.35);
  border-radius: 0px 0px 0px 0px;
  border: 4px solid #AFE4FF;
  border-radius: 50%;
}

:deep(.custom-progress) .el-progress-bar__outer {
  background-color: #6AB1FF !important;
}

.result {
  text-align: center;
}

.Papers {
  width: 100%;
  height: 50px;
  background-image: url('@/assets/images/readerResourcesIcon/modelBackground.png');
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  box-sizing: border-box;
  font-size: 16px;
  margin: 10px 0;

  .collapse-main {
    display: flex;
    align-items: center;

    .icon {
      width: 23px;
      height: 23px;
      display: flex;
      align-items: center;

      & > img {
        width: 100%;
        height: 100%;
      }
    }

    .title {
      width: 70px;
      font-size: 16px;
      text-align: left;
    }

    .name {
      flex: 1;
      padding-left: 10px;
    }
  }
}

.PapersContent {
  width: 100%;
  height: 95vh;
  overflow-y: auto;
  box-sizing: border-box;
  color: #FFFFFF;
}

.custom-btn{
  width: 200px;
  height: 50px;
  background: linear-gradient(to bottom, #F1F6FF, #4F9CF4, #3676ED, #C2E1FF); /* 渐变背景 */
  border: none; /* 去掉默认边框 */
  color: white; /* 字体颜色 */
  padding: 10px 20px; /* 内边距 */
  font-size: 20px; /* 字体大小 */
  border-radius: 30px; /* 圆角 */
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.2); /* 阴影效果 */
  transition: background 0.3s, transform 0.2s; /* 背景和变换过渡效果 */
  cursor: pointer;
}

/* 悬停效果 */
.custom-btn:hover {
  background: linear-gradient(to bottom, #A0BFFF, #0056b3); /* 悬停时的渐变背景 */
  transform: translateY(-2px); /* 悬停时轻微上移 */
}

/* 点击效果 */
.custom-btn:active {
  transform: translateY(2px); /* 点击时下移 */
}


.shadow-on-hover {
  box-shadow: 0 4px 6px rgba(0,0,0,.1), 0 2px 4px rgba(0,0,0,.5);
  transition: all .3s ease;
}

.shadow-on-hover:hover {
  background-color: #2ED2A9; /* 可以根据需要调整颜色 */
  color: white;
  box-shadow: 0 8px 12px rgba(0,0,0,.2), 0 4px 8px rgba(0,0,0,.12); /* 更明显的阴影 */
}


.custom-button {
  position: relative;
  width: 170px;
  height: 50px;
  font-size: 22px;
  line-height: 1.5;
  border-radius: 30px;
  text-align: center; /* 水平居中 */
  vertical-align: middle; /* 垂直居中 */
  background: linear-gradient(to bottom, #F1F6FF, #4F9CF4, #3676ED, #C2E1FF); /* 渐变背景 */
  background-size: cover;
  font-weight: bold;
  color: white;
  border: none;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2), 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  overflow: hidden;
}

.custom-button:active {
  background: linear-gradient(to bottom, #A0BFFF, #0056b3); /* 悬停时的渐变背景 */
  transform: translateY(-2px); /* 悬停时轻微上移 */
}

.start-btn{
  position: absolute;
  text-align: left;
  margin-left: 75px;
  bottom: -90px;
}

.__name__ {
  font-size: 14px;
  color: #666;
  cursor: pointer;
}

.start-title {
  margin-left: 75px;
  font-weight: bold;
  font-size: 42px;
}

.start-question {
  color: #1471DC;
  font-size: 18px;
  margin-bottom: 30px;
  position: relative;
}

.start-facet {
  color: black;
  margin-left: 5%;
  margin-top: 0px !important;
  font-weight: bold;
  font-size: 26px;
  margin-top: 40px;
}

.start-scan-question {
  color: #1471DC;
  font-weight: bold;
  white-space: pre-wrap;
  margin-left: 75px;
  font-size: 26px;
  margin-top: 10px;
  line-height: 1.5;
  display: flex;
}

.end-scan-question {
  color: #1471DC;
  white-space: pre-wrap;
  margin-left: 10%;
  margin-right: 17%;
  height: 250px;
  color: black;
  font-size: 18px;
  margin-top: 60px;
  line-height: 2;
  overflow-y:auto;
}

.container {
  position: relative; /* 设置父元素为相对定位 */
  width: 800px; /* 设置固定宽度 */
  height: 600px; /* 设置固定高度 */
  margin-top: 30px;
}

.start-evaluation-method {
  font-size: 20px;
}

.progress-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;  /* 让文本和进度条左对齐 */
  gap: 10px;  /* 设置文本和进度条之间的间距 */
  margin-right: 5%;
  z-index: -1;
}

.progress-text {
  border: black solid 2px;
  border-radius: 50%;
  background-color: #5BCBFF;
  width: 70px;
  height: 70px;
  margin-left: 20%;
  color: black;
  font-size: 30px;
  top: -25px;
  display: flex; /* 使用 Flexbox */
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
  z-index: 3; /* 圆圈在上层 */
  position: relative; /* 设置为相对定位 */
}

.rectangle {
  height: 60px; /* 高度为60px */
  width: 700px; /* 设置宽度 */
  background-color: #D4F3FF; /* 设置背景颜色 */
  margin-left: -50px;
  border: 2px solid black;
  z-index: 2; /* 确保在圆圈之下 */
  border-radius: 0 30px 30px 0;
  position: relative;
  top: -25px;
}
.custom-progress {
  margin-top: 15px;
  z-index:4;
  font-size: 30px;
  margin-left: 70px;
  margin-right: 30px;
}

.scName {
  background-color: white;
  min-height: 80px; /* 设置最小高度 */
  padding: 10px 20px; /* 适应内容的上下内边距 */
  min-width: 530px; /* 设置最小宽度，以适应最小文字 */
  max-width: 600px;
  width: 530px;
  margin-left: 75px;
  border: 2px #333333 solid;
  position: relative;
  z-index: 2;
  display: grid;
  justify-content: center;
  align-items: center;
}

.overlap-box {
  position: absolute;
  min-height: 85px;
  padding: 10px 20px;
  min-width: 530px;
  top: 7px;
  left: 82px;
  align-self: stretch;
  background-color: #ADBCFF;
  border: 2px black solid;
  z-index: 1;
  box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.2);
}

.overlap-box-big {
  position: absolute;
  min-height: 130px;
  padding: 10px 20px;
  min-width: 530px;
  top: 7px;
  left: 82px;
  align-self: stretch;
  background-color: #ADBCFF;
  border: 2px black solid;
  z-index: 1;
  box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.2);
}

.start-scale{
  color: #1471DC !important;
  text-decoration: none;
  font-weight: bold;
  line-height: 1.2;
  font-size: 42px;
  margin: 0;
}

.container-result {
  display: flex; /* 使用 Flexbox 布局 */
  gap: 20px; /* 列之间的间距 */
  margin-top: 50px;
}

.column {
  display: flex;
  flex-direction: column; /* 每一列是垂直排列的 */
}

.column-row {
  display: grid;
  grid-auto-flow: column;
}

.column-1 {
  flex: 1; /* 第一列占据更多的空间 */
}

.column-2 {
  flex: 1; /* 第二列占据剩余的空间 */
  justify-content: flex-start; /* 水平居中对齐 */
  align-items: flex-start; /* 子元素从顶部开始垂直对齐 */
  height: auto; /* 高度根据内容自动调整 */
  gap: 30px;
  margin-top: 20px; /* 设置顶部外边距 */
  display: flex;
}

.column-3 {
  justify-content: center; /* 水平居中对齐 */
  align-items: flex-start; /* 子元素从顶部开始垂直对齐 */
  flex-wrap: wrap;
  margin-top: 20px; /* 设置顶部外边距 */
  display: flex;
  gap: 20px;
  margin-left: -10px;
}

.column-3-unshow {
  justify-content: center;
  align-items: flex-start;
  flex-wrap: wrap;
  margin-top: 20px;
  display: flex;
  column-gap: 30px; /* 横向间距 */
  row-gap: 40px;   /* 纵向间距 */
}

/* 每行显示三个等宽项目 */
.column-3 > * {
  flex: 0 0 calc(33.333% - 30px); /* 每个占三分之一宽度，减去 gap 影响 */
}

/* 每行显示三个等宽项目 */
.column-3-unshow > * {
  flex: 0 0 calc(33.333% - 30px); /* 每个占三分之一宽度，减去 gap 影响 */
}

.rows-container {
  flex-direction: column; /* 使子元素纵向排列 */
  width: 470px;  /* 使内容充满父容器宽度 */
}

.rows-container-cell {
  width: 470px;
  flex-wrap: wrap !important;
  flex-direction: row!important;
  grid-auto-flow: column !important;
}

.rows-container-cell-unshow {
  flex-wrap: wrap !important;
  flex-direction: row!important;
  grid-auto-flow: column !important;
}

.module1 {
  padding: 20px;
  border-radius: 8px;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}
.module2 {
  padding: 20px;
  border-radius: 8px;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}

.colu1{
  width:430px;
  height: 430px;
  background-color: white;
  border-radius: 20px;
  border: 1px solid #333333;
  padding:50px;
  margin-left: 70px;
}

.colu2{
  width:430px;
  height: 380px;
  background-color: white;
  border-radius: 20px;
  border: 1px solid #333333;
  padding:20px;
  margin-left: 20px;
}

.row {
  background-color: #ffffff;
  padding: 10px 40px;
  height: 85px;
  border-radius: 12px;
  border: 1px solid #333333;
  color: #333333;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 26px;
  font-weight: bold;
}

.row-unshow {
  background-color: #ffffff;
  padding: 0px 40px;
  height: 90px;
  border-radius: 12px;
  border: 1px solid #333333;
  color: #333333;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 26px;
  font-weight: bold;
}

.image-container {
  position: relative;  /* 设置相对定位，使得可以使用绝对定位定位“60分” */
}

.score {
  position: absolute;
  top: 50%;  /* 垂直居中 */
  left: 50%;  /* 水平居中 */
  transform: translate(-50%, -50%);  /* 通过负的50%偏移，使元素真正居中 */
  font-size: 24px;  /* 你可以调整字体大小 */
  font-weight: bold;  /* 使“60分”加粗 */
}

.cell {
  background-color: #ffffff;
  flex-direction: column;
  padding: 10px 60px;
  height: 170px;
  border-radius: 12px;
  border: 1px solid #333333;
  color: #333333;
  display: flex;
  justify-content: space-between;
}

.cell-unshow {
  background-color: #ffffff;
  flex-direction: column;
  padding: 10px 30px;
  /*  width: 100px;*/
  height: 170px;
  border-radius: 12px;
  border: 1px solid #333333;
  color: #333333;
  display: flex;
  gap: 10px !important;
  justify-content: space-between;
}

.else-row{

}

.left-item-cell {
  font-weight: bold;
  font-size: 30px;/* 可选: 可以加粗左边文字 */
  margin-top: 25px;
  margin-left: -80px !important;
}

.right-item-cell {
  font-size: 26px; /* 可选: 控制右边得分的字体大小 */
  margin-left: -80px !important;
  margin-bottom: 20px;
}

:deep .el-progress__text {
  font-size: 30px !important;
}

.start-scan-question-container {
  gap: 10px;
  overflow-y: auto;
  max-height: 500px;
  overflow-y: auto;
  margin-top: 20px;
}

.dot {
  width: 20px;  /* 设置原点的宽度 */
  text-align: center;
  margin-right: 10px;  /* 原点和文字之间的间距 */
}

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: lightskyblue;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}
/*.content {
  width: 100%;
  padding: 0 20px;
  box-sizing: border-box;
  margin-top: -60px;
  overflow-y: auto
}*/
</style>
