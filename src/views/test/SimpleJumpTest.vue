<template>
  <div class="simple-jump-test">
    <h2>简单跳转测试</h2>
    
    <div class="info">
      <p><strong>当前环境:</strong> {{ isStandalone ? '独立新窗口' : '正常窗口' }}</p>
      <p><strong>有父窗口:</strong> {{ hasOpener ? '是' : '否' }}</p>
    </div>

    <div class="test-section">
      <h3>测试知识点跳转</h3>
      <question-hint 
        :sectionReferToData="testData"
        analysis="这是一个简单的测试，点击下面的知识点应该能跳转到对应章节。"
      />
    </div>

    <div class="manual-test">
      <h3>手动测试</h3>
      <el-button @click="sendTestMessage" type="primary">
        发送测试跳转消息
      </el-button>
      <p class="tip">点击后应该跳转到测试章节</p>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElButton, ElMessage } from 'element-plus'
import questionHint from '@/views/reader/sub/PageModule/question-hint/questionHint.vue'
import { isInStandaloneWindow } from '@/utils/windowStoreSync'

// 计算属性
const isStandalone = computed(() => isInStandaloneWindow())
const hasOpener = computed(() => !!window.opener)

// 测试数据
const testData = ref([
  [
    { name: '第一章', chapterId: 'chapter-1', bookId: 'test-book' },
    { name: '基础知识', chapterId: 'chapter-1', bookId: 'test-book', domId: 'section-1-1' }
  ],
  [
    { name: '第二章', chapterId: 'chapter-2', bookId: 'test-book' },
    { name: '进阶内容', chapterId: 'chapter-2', bookId: 'test-book', domId: 'section-2-1' }
  ]
])

// 手动发送测试消息
function sendTestMessage() {
  if (!window.opener) {
    ElMessage.error('没有父窗口，无法发送消息')
    return
  }

  const testHeadingItem = {
    name: '测试章节',
    chapterId: 'test-chapter',
    bookId: 'test-book',
    domId: 'test-section'
  }

  console.log('🚀 发送测试消息:', testHeadingItem)

  window.opener.postMessage({
    type: 'JUMP_TO_SECTION',
    data: testHeadingItem
  }, window.location.origin)

  ElMessage.success('测试消息已发送')

  setTimeout(() => {
    window.close()
  }, 1000)
}
</script>

<style scoped>
.simple-jump-test {
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
}

.info {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.manual-test {
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  text-align: center;
}

.tip {
  margin-top: 10px;
  color: #666;
  font-size: 14px;
}
</style>
