<template>
  <div class="window-store-sync-test">
    <h2>新窗口 Store 同步测试页面</h2>
    
    <div class="test-section">
      <h3>当前 Store 状态</h3>
      <div class="store-info">
        <p><strong>书籍ID:</strong> {{ store.comprehensiveBookData?.bookId || '未设置' }}</p>
        <p><strong>章节ID:</strong> {{ store.chapterId || '未设置' }}</p>
        <p><strong>简化模式:</strong> {{ store.simplifiedMode ? '是' : '否' }}</p>
        <p><strong>章节数据长度:</strong> {{ store.comprehensiveChapterAndCatalogData?.chaptersData?.length || 0 }}</p>
        <p><strong>总页数:</strong> {{ store.comprehensiveBookData?.totalPages || 0 }}</p>
        <p><strong>是否在新窗口:</strong> {{ isNewWindow ? '是' : '否' }}</p>
        <p><strong>是否在独立窗口:</strong> {{ isStandaloneWindow ? '是' : '否' }}</p>
      </div>
    </div>

    <div class="test-section">
      <h3>URL 参数信息</h3>
      <div class="url-info">
        <p><strong>URL 书籍ID:</strong> {{ urlBookId || '未找到' }}</p>
        <p><strong>URL 章节ID:</strong> {{ urlChapterId || '未找到' }}</p>
        <p><strong>URL 目录ID:</strong> {{ urlCatalogId || '未找到' }}</p>
      </div>
    </div>

    <div class="test-section">
      <h3>操作按钮</h3>
      <div class="actions">
        <el-button @click="initializeData" :loading="initializing">
          手动初始化数据
        </el-button>
        <el-button @click="debugStore">
          打印调试信息
        </el-button>
        <el-button @click="testJump" :disabled="!canJump">
          测试跳转功能
        </el-button>
        <el-button @click="openNewWindow">
          打开新窗口测试
        </el-button>
        <el-button @click="testParentWindowJump" v-if="isStandaloneWindow">
          测试跳转到父窗口
        </el-button>
      </div>
    </div>

    <div class="test-section">
      <h3>测试用知识点数据</h3>
      <question-hint 
        :sectionReferToData="testSectionData"
        analysis="这是一个测试解析内容"
      />
    </div>

    <div class="test-section">
      <h3>日志输出</h3>
      <div class="log-output" ref="logOutput">
        <div v-for="(log, index) in logs" :key="index" :class="log.type">
          [{{ log.time }}] {{ log.message }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { ElButton, ElMessage } from 'element-plus'
import useReader from '@/store/modules/reader'
import questionHint from '@/views/reader/sub/PageModule/question-hint/questionHint.vue'
import {
  isInNewWindow,
  getBookIdFromUrl,
  getChapterIdFromUrl,
  getCatalogIdFromUrl,
  autoInitializeIfNeeded,
  debugStoreState,
  safeJumpToSection,
  isInStandaloneWindow,
  jumpToParentWindow
} from '@/utils/windowStoreSync'

const store = useReader()
const initializing = ref(false)
const logs = ref([])
const logOutput = ref(null)

// 计算属性
const isNewWindow = computed(() => isInNewWindow(store))
const isStandaloneWindow = computed(() => isInStandaloneWindow())
const urlBookId = computed(() => getBookIdFromUrl())
const urlChapterId = computed(() => getChapterIdFromUrl())
const urlCatalogId = computed(() => getCatalogIdFromUrl())
const canJump = computed(() => {
  return store.comprehensiveChapterAndCatalogData?.chaptersData?.length > 0
})

// 测试用的知识点数据
const testSectionData = ref([
  [
    { name: '第一章', chapterId: 'chapter1', bookId: urlBookId.value || 'test-book' },
    { name: '第一节', chapterId: 'chapter1', bookId: urlBookId.value || 'test-book', domId: 'section1' }
  ],
  [
    { name: '第二章', chapterId: 'chapter2', bookId: urlBookId.value || 'test-book' },
    { name: '第二节', chapterId: 'chapter2', bookId: urlBookId.value || 'test-book', domId: 'section2' }
  ]
])

// 添加日志
function addLog(message, type = 'info') {
  const time = new Date().toLocaleTimeString()
  logs.value.push({ message, type, time })
  nextTick(() => {
    if (logOutput.value) {
      logOutput.value.scrollTop = logOutput.value.scrollHeight
    }
  })
}

// 手动初始化数据
async function initializeData() {
  initializing.value = true
  addLog('开始手动初始化数据...', 'info')
  
  try {
    await autoInitializeIfNeeded(store)
    addLog('数据初始化成功', 'success')
  } catch (error) {
    addLog(`数据初始化失败: ${error.message}`, 'error')
  } finally {
    initializing.value = false
  }
}

// 调试 Store
function debugStore() {
  debugStoreState(store, 'Manual Debug')
  addLog('调试信息已输出到控制台', 'info')
}

// 测试跳转
async function testJump() {
  const bookId = urlBookId.value || 'test-book'
  const chapterId = store.comprehensiveChapterAndCatalogData.chaptersData[0]?.chapterId
  
  if (!chapterId) {
    ElMessage.error('没有可用的章节进行测试')
    return
  }
  
  addLog(`测试跳转到章节: ${chapterId}`, 'info')
  
  try {
    await safeJumpToSection(store, bookId, chapterId)
    addLog('跳转测试成功', 'success')
  } catch (error) {
    addLog(`跳转测试失败: ${error.message}`, 'error')
  }
}

// 打开新窗口
function openNewWindow() {
  const bookId = urlBookId.value || 'test-book'
  const url = `/test/window-store-sync?k=${bookId}&cid=chapter1&cataid=catalog1`
  window.open(url, '_blank')
  addLog(`打开新窗口: ${url}`, 'info')
}

// 测试跳转到父窗口
function testParentWindowJump() {
  const bookId = urlBookId.value || 'test-book'
  const chapterId = 'chapter1'
  const domId = 'section1'

  addLog(`测试跳转到父窗口: ${chapterId}`, 'info')

  const success = jumpToParentWindow(bookId, chapterId, domId)
  if (success) {
    addLog('跳转请求已发送', 'success')
  } else {
    addLog('跳转请求发送失败', 'error')
  }
}

onMounted(() => {
  addLog('测试页面已加载', 'info')
  debugStoreState(store, 'Page Mounted')
})
</script>

<style scoped>
.window-store-sync-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.test-section h3 {
  margin-top: 0;
  color: #333;
}

.store-info, .url-info {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
}

.store-info p, .url-info p {
  margin: 5px 0;
}

.actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.log-output {
  height: 200px;
  overflow-y: auto;
  background: #1e1e1e;
  color: #fff;
  padding: 10px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log-output .info {
  color: #fff;
}

.log-output .success {
  color: #4caf50;
}

.log-output .error {
  color: #f44336;
}

.log-output .warning {
  color: #ff9800;
}
</style>
