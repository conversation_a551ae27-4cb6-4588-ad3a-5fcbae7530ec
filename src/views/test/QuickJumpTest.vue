<template>
  <div class="quick-jump-test">
    <h2>快速跳转测试</h2>
    
    <div class="test-info">
      <h3>当前环境</h3>
      <ul>
        <li><strong>是否有父窗口:</strong> {{ hasOpener ? '是' : '否' }}</li>
        <li><strong>是否在独立窗口:</strong> {{ isStandalone ? '是' : '否' }}</li>
        <li><strong>当前URL:</strong> {{ currentUrl }}</li>
        <li><strong>书籍ID:</strong> {{ bookId || '未找到' }}</li>
      </ul>
    </div>

    <div class="test-actions">
      <h3>测试操作</h3>
      
      <div class="action-group">
        <h4>1. 消息通信测试</h4>
        <el-button @click="testMessage" type="primary">发送测试消息</el-button>
        <p class="tip">点击后检查父窗口是否弹出提示框</p>
      </div>

      <div class="action-group">
        <h4>2. 跳转功能测试</h4>
        <el-button @click="testPostMessageJump" type="success">测试 PostMessage 跳转</el-button>
        <el-button @click="testFallbackJump" type="warning">测试备用跳转</el-button>
        <el-button @click="testSmartJump" type="info">测试智能跳转</el-button>
        <el-button @click="testJumpFunctionality" type="primary">测试跳转功能</el-button>
        <el-button @click="debugCurrentStore" type="default">调试 Store 状态</el-button>
      </div>

      <div class="action-group">
        <h4>3. 实际场景测试</h4>
        <div class="knowledge-point-demo">
          <question-hint 
            :sectionReferToData="demoData"
            analysis="这是一个测试解析内容，用于验证跳转功能是否正常工作。"
          />
        </div>
      </div>
    </div>

    <div class="console-output">
      <h3>控制台输出</h3>
      <div class="console-box" ref="consoleBox">
        <div v-for="(log, index) in consoleLogs" :key="index" :class="['log-line', log.type]">
          [{{ log.time }}] {{ log.message }}
        </div>
      </div>
      <el-button @click="clearLogs" size="small">清空日志</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { ElButton } from 'element-plus'
import questionHint from '@/views/reader/sub/PageModule/question-hint/questionHint.vue'
import {
  isInStandaloneWindow,
  jumpToParentWindow,
  fallbackJumpToParent,
  smartJumpToSection,
  getBookIdFromUrl,
  testJumpFunction,
  debugStoreState
} from '@/utils/windowStoreSync'
import { 
  testParentWindowMessage,
  manualTriggerJumpRequest,
  checkWindowRelationship
} from '@/utils/messageTestHelper'
import useReader from '@/store/modules/reader'

const store = useReader()
const consoleLogs = ref([])
const consoleBox = ref(null)

// 计算属性
const hasOpener = computed(() => !!window.opener)
const isStandalone = computed(() => isInStandaloneWindow())
const currentUrl = computed(() => window.location.href)
const bookId = computed(() => getBookIdFromUrl())

// 演示数据
const demoData = ref([
  [
    { name: '第一章', chapterId: 'demo-chapter-1', bookId: bookId.value || 'demo-book' },
    { name: '基础概念', chapterId: 'demo-chapter-1', bookId: bookId.value || 'demo-book', domId: 'section-1-1' }
  ],
  [
    { name: '第二章', chapterId: 'demo-chapter-2', bookId: bookId.value || 'demo-book' },
    { name: '进阶内容', chapterId: 'demo-chapter-2', bookId: bookId.value || 'demo-book', domId: 'section-2-1' }
  ]
])

// 添加日志
function addLog(message, type = 'info') {
  const time = new Date().toLocaleTimeString()
  consoleLogs.value.push({ message, type, time })
  
  nextTick(() => {
    if (consoleBox.value) {
      consoleBox.value.scrollTop = consoleBox.value.scrollHeight
    }
  })
}

// 清空日志
function clearLogs() {
  consoleLogs.value = []
}

// 测试消息通信
function testMessage() {
  addLog('开始测试消息通信...', 'info')
  const success = testParentWindowMessage()
  if (success) {
    addLog('测试消息已发送，请检查父窗口', 'success')
  } else {
    addLog('发送测试消息失败', 'error')
  }
}

// 测试 PostMessage 跳转
function testPostMessageJump() {
  const testBookId = bookId.value || 'demo-book'
  const testChapterId = 'demo-chapter-1'
  const testDomId = 'section-1-1'
  
  addLog(`测试 PostMessage 跳转: ${testChapterId}`, 'info')
  
  const success = jumpToParentWindow(testBookId, testChapterId, testDomId)
  if (success) {
    addLog('PostMessage 跳转请求已发送', 'success')
  } else {
    addLog('PostMessage 跳转失败', 'error')
  }
}

// 测试备用跳转
function testFallbackJump() {
  const testBookId = bookId.value || 'demo-book'
  const testChapterId = 'demo-chapter-2'
  const testDomId = 'section-2-1'
  
  addLog(`测试备用跳转: ${testChapterId}`, 'info')
  
  const success = fallbackJumpToParent(testBookId, testChapterId, testDomId)
  if (success) {
    addLog('备用跳转请求已发送', 'success')
  } else {
    addLog('备用跳转失败', 'error')
  }
}

// 测试智能跳转
async function testSmartJump() {
  const testBookId = bookId.value || 'demo-book'
  const testChapterId = 'demo-chapter-1'
  const testDomId = 'section-1-1'

  addLog(`测试智能跳转: ${testChapterId}`, 'info')

  try {
    await smartJumpToSection(store, testBookId, testChapterId, testDomId)
    addLog('智能跳转成功', 'success')
  } catch (error) {
    addLog(`智能跳转失败: ${error.message}`, 'error')
  }
}

// 测试跳转功能
async function testJumpFunctionality() {
  const testBookId = bookId.value || 'demo-book'
  const testChapterId = 'demo-chapter-1'
  const testDomId = 'section-1-1'

  addLog(`测试跳转功能: ${testChapterId}`, 'info')

  try {
    const success = await testJumpFunction(store, testBookId, testChapterId, testDomId)
    if (success) {
      addLog('跳转功能测试成功', 'success')
    } else {
      addLog('跳转功能测试失败', 'error')
    }
  } catch (error) {
    addLog(`跳转功能测试异常: ${error.message}`, 'error')
  }
}

// 调试当前 Store 状态
function debugCurrentStore() {
  addLog('正在调试 Store 状态，请查看控制台', 'info')
  debugStoreState(store, 'Manual Debug from Test Page')
}

onMounted(() => {
  addLog('快速跳转测试页面已加载', 'info')
  
  const windowInfo = checkWindowRelationship()
  addLog(`窗口信息: ${JSON.stringify(windowInfo)}`, 'info')
})
</script>

<style scoped>
.quick-jump-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-info {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.test-info ul {
  margin: 10px 0;
  padding-left: 20px;
}

.test-actions {
  margin-bottom: 20px;
}

.action-group {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.action-group h4 {
  margin-top: 0;
  color: #333;
}

.action-group .el-button {
  margin-right: 10px;
  margin-bottom: 10px;
}

.tip {
  font-size: 12px;
  color: #666;
  margin-top: 5px;
}

.knowledge-point-demo {
  background: #fafafa;
  padding: 15px;
  border-radius: 4px;
  margin-top: 10px;
}

.console-output {
  border-top: 1px solid #e0e0e0;
  padding-top: 20px;
}

.console-box {
  height: 200px;
  overflow-y: auto;
  background: #1e1e1e;
  color: #fff;
  padding: 10px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  margin-bottom: 10px;
}

.log-line {
  margin-bottom: 2px;
  padding: 1px 0;
}

.log-line.info {
  color: #fff;
}

.log-line.success {
  color: #4caf50;
}

.log-line.error {
  color: #f44336;
}

.log-line.warning {
  color: #ff9800;
}
</style>
