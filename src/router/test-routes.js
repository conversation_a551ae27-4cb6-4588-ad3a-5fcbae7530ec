/**
 * 测试路由配置
 * 用于测试新窗口 Store 同步功能
 */

export const testRoutes = [
  {
    path: '/test/window-store-sync',
    name: 'WindowStoreSyncTest',
    component: () => import('@/views/test/WindowStoreSyncTest.vue'),
    meta: {
      title: '新窗口 Store 同步测试',
      requiresAuth: false
    }
  }
]

// 如果需要添加到主路由配置中，可以这样使用：
// import { testRoutes } from './test-routes'
// 
// const routes = [
//   ...existingRoutes,
//   ...testRoutes
// ]
