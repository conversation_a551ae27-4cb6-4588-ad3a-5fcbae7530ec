<template>
  <div class="smart-jump-example">
    <h3>智能跳转示例</h3>
    
    <!-- 环境信息显示 -->
    <div class="environment-info">
      <p><strong>当前环境:</strong> 
        <span v-if="isStandaloneWindow" class="tag standalone">独立新窗口</span>
        <span v-else-if="isNewWindow" class="tag new-window">新窗口</span>
        <span v-else class="tag normal">正常窗口</span>
      </p>
      <p v-if="isStandaloneWindow" class="tip">
        <i class="el-icon-info"></i>
        在独立新窗口中，点击知识点将跳转到主阅读器页面
      </p>
    </div>

    <!-- 知识点列表 -->
    <div class="knowledge-points">
      <h4>知识点列表</h4>
      <ul>
        <li v-for="(point, index) in knowledgePoints" :key="index">
          <el-button 
            @click="jumpToKnowledgePoint(point)"
            :loading="jumpingIndex === index"
            size="small"
            type="primary"
            plain
          >
            {{ point.name }}
            <span v-if="isStandaloneWindow" class="jump-hint">→ 主页面</span>
          </el-button>
        </li>
      </ul>
    </div>

    <!-- 操作日志 -->
    <div class="operation-log">
      <h4>操作日志</h4>
      <div class="log-container">
        <div v-for="(log, index) in logs" :key="index" :class="['log-item', log.type]">
          [{{ log.time }}] {{ log.message }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { ElButton, ElMessage } from 'element-plus'
import useReader from '@/store/modules/reader'
import { 
  smartJumpToSection,
  isInNewWindow,
  isInStandaloneWindow,
  getBookIdFromUrl,
  autoInitializeIfNeeded,
  debugStoreState
} from '@/utils/windowStoreSync'

const store = useReader()
const jumpingIndex = ref(-1)
const logs = ref([])

// 计算属性
const isNewWindow = computed(() => isInNewWindow(store))
const isStandaloneWindow = computed(() => isInStandaloneWindow())

// 示例知识点数据
const knowledgePoints = ref([
  {
    name: '第一章 基础概念',
    bookId: getBookIdFromUrl() || 'demo-book',
    chapterId: 'chapter-1',
    domId: 'section-1-1'
  },
  {
    name: '第二章 进阶内容',
    bookId: getBookIdFromUrl() || 'demo-book',
    chapterId: 'chapter-2',
    domId: 'section-2-1'
  },
  {
    name: '第三章 实践应用',
    bookId: getBookIdFromUrl() || 'demo-book',
    chapterId: 'chapter-3',
    domId: null // 没有具体定位，只跳转到章节
  }
])

// 添加日志
function addLog(message, type = 'info') {
  const time = new Date().toLocaleTimeString()
  logs.value.unshift({ message, type, time })
  
  // 限制日志数量
  if (logs.value.length > 10) {
    logs.value = logs.value.slice(0, 10)
  }
}

// 跳转到知识点
async function jumpToKnowledgePoint(point, index) {
  jumpingIndex.value = index
  
  addLog(`开始跳转到: ${point.name}`, 'info')
  
  try {
    await smartJumpToSection(store, point.bookId, point.chapterId, point.domId)
    addLog(`跳转成功: ${point.name}`, 'success')
  } catch (error) {
    addLog(`跳转失败: ${error.message}`, 'error')
    ElMessage.error('跳转失败: ' + error.message)
  } finally {
    jumpingIndex.value = -1
  }
}

// 组件挂载时初始化
onMounted(async () => {
  addLog('组件已挂载', 'info')
  debugStoreState(store, 'Smart Jump Example Mounted')
  
  try {
    await autoInitializeIfNeeded(store)
    addLog('数据初始化完成', 'success')
  } catch (error) {
    addLog(`数据初始化失败: ${error.message}`, 'warning')
  }
})
</script>

<style scoped>
.smart-jump-example {
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
}

.environment-info {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.tag {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.tag.standalone {
  background: #e6f7ff;
  color: #1890ff;
}

.tag.new-window {
  background: #fff7e6;
  color: #fa8c16;
}

.tag.normal {
  background: #f6ffed;
  color: #52c41a;
}

.tip {
  margin-top: 10px;
  color: #666;
  font-size: 14px;
}

.knowledge-points {
  margin-bottom: 20px;
}

.knowledge-points ul {
  list-style: none;
  padding: 0;
}

.knowledge-points li {
  margin-bottom: 10px;
}

.jump-hint {
  font-size: 12px;
  opacity: 0.7;
  margin-left: 5px;
}

.operation-log {
  border-top: 1px solid #e0e0e0;
  padding-top: 20px;
}

.log-container {
  height: 200px;
  overflow-y: auto;
  background: #fafafa;
  padding: 10px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log-item {
  margin-bottom: 5px;
  padding: 2px 0;
}

.log-item.info {
  color: #333;
}

.log-item.success {
  color: #52c41a;
}

.log-item.error {
  color: #ff4d4f;
}

.log-item.warning {
  color: #fa8c16;
}
</style>
