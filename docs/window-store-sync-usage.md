# 新窗口 Store 数据同步解决方案

## 问题描述

在使用 `window.open` 打开新窗口时，新窗口中的 Vuex store 数据为空，导致依赖 store 数据的功能（如章节跳转）无法正常工作。

### 具体问题
1. `store.comprehensiveChapterAndCatalogData.chaptersData` 为空数组
2. `store.chapterId` 为 undefined，导致 `loadChapterData` 方法中的逻辑判断失效
3. `store.simplifiedMode` 为 undefined，影响章节内容获取方式的选择
4. 其他关键状态未正确初始化

## 解决方案

### 方案一：智能跳转（推荐）

这是最用户友好的解决方案，会自动检测当前环境并选择最佳跳转策略：
- 如果在独立新窗口中，会跳转到父窗口（主阅读器页面）
- 如果在当前窗口中，会在当前窗口内跳转

### 方案二：自动数据初始化

在新窗口中自动重新获取必要的数据并在当前窗口内跳转。

#### 使用方法

#### 方案一使用方法

1. **在组件中导入工具函数**：
```javascript
import { smartJumpToSection, autoInitializeIfNeeded } from '@/utils/windowStoreSync'
```

2. **在组件挂载时自动初始化**：
```javascript
onMounted(async () => {
  try {
    await autoInitializeIfNeeded(store)
  } catch (error) {
    console.warn('自动初始化失败，将在点击时重试:', error)
  }
})
```

3. **使用智能跳转方法**：
```javascript
async function jumpToSection(chapterId, domId = null) {
  try {
    const bookId = getBookIdFromUrl() // 从 URL 获取书籍ID
    await smartJumpToSection(store, bookId, chapterId, domId)
  } catch (error) {
    ElMessage.error('跳转失败: ' + error.message)
  }
}
```

4. **在主阅读器页面添加监听器**：
```javascript
import { listenForJumpRequests } from '@/utils/windowStoreSync'

onMounted(() => {
  const cleanupJumpListener = listenForJumpRequests(store)

  onBeforeUnmount(() => {
    cleanupJumpListener()
  })
})
```

#### 方案二使用方法

1. **使用安全跳转方法**：
```javascript
async function jumpToSection(chapterId, domId = null) {
  try {
    const bookId = getBookIdFromUrl() // 从 URL 获取书籍ID
    await safeJumpToSection(store, bookId, chapterId, domId)
  } catch (error) {
    ElMessage.error('跳转失败: ' + error.message)
  }
}
```

#### 优点
- 实现简单，无需修改现有的 window.open 调用
- 自动处理数据初始化
- 对现有代码影响最小

#### 缺点
- 需要重新请求数据，可能有轻微的延迟
- 依赖 URL 参数中的书籍ID

### 方案二：postMessage 数据传递

通过 postMessage API 在父子窗口间传递数据。

#### 使用方法

1. **父窗口发送数据**：
```javascript
import { sendStoreDataToChildWindow } from '@/utils/windowStoreSync'

// 打开新窗口
const childWindow = window.open('/reader?k=bookId', '_blank')

// 等待子窗口加载完成后发送数据
childWindow.addEventListener('load', () => {
  sendStoreDataToChildWindow(childWindow, store)
})
```

2. **子窗口接收数据**：
```javascript
import { listenForStoreDataFromParent } from '@/utils/windowStoreSync'

onMounted(() => {
  // 监听父窗口发送的数据
  const cleanup = listenForStoreDataFromParent(store)
  
  // 在组件卸载时清理监听器
  onUnmounted(cleanup)
})
```

#### 优点
- 数据传递快速，无需重新请求
- 可以传递完整的 store 状态

#### 缺点
- 需要修改打开新窗口的代码
- 实现相对复杂

## 工具函数说明

### `isInNewWindow(store)`
检查当前是否在新窗口中（通过检查 store 数据是否为空）。

### `initializeStoreDataInNewWindow(store, bookId)`
在新窗口中初始化必要的 store 数据。

### `safeJumpToSection(store, bookId, chapterId, domId)`
安全的章节跳转方法，自动处理新窗口数据初始化。

### `getBookIdFromUrl()`
从 URL 参数中获取书籍ID。

### `autoInitializeIfNeeded(store)`
检查并自动初始化新窗口的 store 数据。

## 实际应用示例

### questionHint.vue 组件的修改

```javascript
// 导入工具函数
import { safeJumpToSection, getBookIdFromUrl, autoInitializeIfNeeded } from '@/utils/windowStoreSync'

// 组件挂载时自动初始化
onMounted(async () => {
  try {
    await autoInitializeIfNeeded(store)
  } catch (error) {
    console.warn('自动初始化失败，将在点击时重试:', error)
  }
})

// 简化的跳转函数
async function clickToGoToReferredSection(clickedSectionItem) {
  const headingItem = clickedSectionItem[clickedSectionItem.length - 1]
  
  try {
    const bookId = headingItem.bookId || getBookIdFromUrl()
    
    if (!bookId) {
      ElMessage.error('无法获取书籍信息')
      return
    }
    
    // 使用安全跳转方法
    await safeJumpToSection(store, bookId, headingItem.chapterId, headingItem.domId)
    
    // 高亮显示
    if (headingItem.domId) {
      const level = clickedSectionItem.length - 1
      const targetHeadingDom = document.querySelector(`h${level}[${HEADING_ATTR_ID}="${headingItem.domId}"]`)
      if (targetHeadingDom) {
        highlightKeyWordSmooth(targetHeadingDom)
      }
    }
  } catch (err) {
    ElMessage.error(err.message || '跳转失败，请稍后重试')
    console.error('跳转错误:', err)
  }
}
```

## 关键问题解决

### loadChapterData 方法中的状态问题

原问题：`loadChapterData` 方法中使用了 `this.chapterId` 和 `this.simplifiedMode`，在新窗口中这些值为空。

解决方案：
1. **自动设置 simplifiedMode**：在数据初始化时自动设置为 `true`
2. **智能设置 chapterId**：
   - 优先从 URL 参数获取章节ID
   - 如果没有，使用第一个章节作为默认值
   - 在跳转前确保设置正确的章节ID

```javascript
// 在 initializeStoreDataInNewWindow 中
store.setSimplifiedMode(true)
if (chapterInfoArray.length > 0) {
  store.setCurrentChapterId(chapterInfoArray[0].chapterId)
}

// 在 safeJumpToSection 中
if (store.chapterId !== chapterId) {
  store.setCurrentChapterId(chapterId)
}
```

## 调试工具

提供了 `debugStoreState` 函数来帮助调试：

```javascript
import { debugStoreState } from '@/utils/windowStoreSync'

// 在任何地方调用
debugStoreState(store, '调试上下文')
```

## 测试页面

创建了专门的测试页面 `/test/window-store-sync`，包含：
- Store 状态显示
- URL 参数信息
- 手动初始化按钮
- 跳转功能测试
- 实时日志输出

## 注意事项

1. 确保 URL 中包含必要的参数（如书籍ID）
2. 处理网络请求失败的情况
3. 在组件卸载时清理事件监听器
4. 考虑用户体验，在数据加载时显示适当的提示信息
5. **重要**：确保 `store.chapterId` 和 `store.simplifiedMode` 在调用 `loadChapterData` 前已正确设置

## 测试建议

1. 使用测试页面 `/test/window-store-sync` 进行全面测试
2. 测试在新窗口中直接访问包含 questionHint 组件的页面
3. 测试网络请求失败的情况
4. 测试不同的章节跳转场景（有 domId 和无 domId）
5. 测试在没有书籍ID的情况下的错误处理
6. 检查控制台输出的调试信息，确认状态设置正确
