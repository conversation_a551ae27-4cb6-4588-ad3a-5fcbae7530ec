# 新窗口跳转问题故障排除指南

## 问题现象
- 在新窗口中点击知识点
- 窗口关闭了
- 但主阅读器页面没有跳转到对应章节

## 调试步骤

### 1. 启用调试模式

在 URL 中添加 `?debug=true` 或在浏览器控制台中执行：
```javascript
localStorage.setItem('enableMessageDebug', 'true')
```

然后刷新页面，会出现调试面板。

### 2. 检查控制台日志

打开浏览器开发者工具，查看控制台输出：

#### 子窗口应该看到的日志：
```
🚀 尝试跳转到父窗口: {bookId: "xxx", chapterId: "xxx", domId: "xxx"}
📤 向父窗口发送跳转请求: {type: "JUMP_TO_SECTION", data: {...}}
✅ 跳转请求已发送，准备关闭窗口
🔒 关闭当前窗口
```

#### 父窗口应该看到的日志：
```
🎧 开始监听子窗口跳转请求
📨 收到消息: {type: "JUMP_TO_SECTION", data: {...}}
🎯 收到子窗口跳转请求: {bookId: "xxx", chapterId: "xxx", domId: "xxx"}
✅ 开始执行跳转...
🎉 跳转成功
```

### 3. 常见问题及解决方案

#### 问题1：父窗口没有收到消息
**可能原因：**
- 父窗口的监听器没有正确设置
- 消息被浏览器安全策略阻止

**解决方案：**
1. 确保主阅读器页面已正确导入并调用 `listenForJumpRequests`
2. 检查浏览器控制台是否有安全策略错误
3. 使用调试面板的"测试消息"功能验证通信

#### 问题2：收到消息但没有跳转
**可能原因：**
- Store 数据未初始化
- 书籍ID不匹配
- 章节不存在

**解决方案：**
1. 检查控制台日志中的 store 状态
2. 确认书籍ID是否匹配
3. 验证章节数据是否已加载

#### 问题3：窗口关闭太快
**可能原因：**
- 消息发送后立即关闭窗口，消息可能丢失

**解决方案：**
- 已增加延迟时间到500ms
- 如果仍有问题，可以手动增加延迟

### 4. 手动测试步骤

1. **测试消息通信：**
   - 在新窗口中点击调试面板的"测试消息"按钮
   - 检查父窗口是否弹出提示框

2. **测试跳转请求：**
   - 在新窗口中点击调试面板的"测试跳转"按钮
   - 观察父窗口的控制台日志

3. **检查窗口关系：**
   - 在控制台执行 `window.opener` 检查是否有父窗口
   - 执行 `window.location.origin` 检查域名

### 5. 代码检查清单

#### 主阅读器页面 (reader/index.vue)
- [ ] 已导入 `listenForJumpRequests`
- [ ] 在 `onMounted` 中调用监听器
- [ ] 在 `onBeforeUnmount` 中清理监听器

#### 子窗口页面 (questionHint.vue)
- [ ] 已导入 `smartJumpToSection`
- [ ] 正确调用跳转函数
- [ ] 传递了正确的参数

#### 工具函数 (windowStoreSync.js)
- [ ] `isInStandaloneWindow` 返回正确值
- [ ] `jumpToParentWindow` 正确发送消息
- [ ] `listenForJumpRequests` 正确处理消息

### 6. 浏览器兼容性

某些浏览器可能有不同的 `postMessage` 行为：

- **Chrome/Edge**: 通常工作正常
- **Firefox**: 可能需要额外的安全设置
- **Safari**: 可能有跨域限制

### 7. 临时解决方案

如果跨窗口通信仍有问题，可以使用以下临时方案：

```javascript
// 在新窗口中直接跳转到主页面
function fallbackJump(bookId, chapterId, domId) {
  const url = `/reader?k=${bookId}&cid=${chapterId}${domId ? `&domId=${domId}` : ''}`
  window.opener.location.href = url
  window.close()
}
```

### 8. 获取更多帮助

如果问题仍然存在：

1. 收集完整的控制台日志
2. 记录浏览器版本和操作系统
3. 提供重现步骤
4. 检查网络请求是否有错误

## 调试命令

在浏览器控制台中可以使用的调试命令：

```javascript
// 检查窗口关系
console.log('Has opener:', !!window.opener)
console.log('Is top:', window === window.top)

// 手动发送测试消息
if (window.opener) {
  window.opener.postMessage({
    type: 'TEST_MESSAGE',
    data: { test: true }
  }, window.location.origin)
}

// 检查监听器
console.log('Event listeners:', getEventListeners(window))
```
